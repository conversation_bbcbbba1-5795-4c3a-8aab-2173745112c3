const fs = require('fs');
const path = require('path');

const backendPath = path.resolve(__dirname, '../packages/backend');

if (!fs.existsSync(backendPath)) {
  fs.mkdirSync(backendPath, { recursive: true });
  fs.writeFileSync(
    path.join(backendPath, 'index.js'),
    '// Placeholder backend package for EAS build\n'
  );
  console.log('Created placeholder packages/backend');
} else {
  console.log('packages/backend already exists');
} 