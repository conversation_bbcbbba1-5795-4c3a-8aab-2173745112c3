{"name": "@workspace/backend", "version": "1.0.0", "description": "", "main": "./convex/_generated/api.js", "types": "./convex/_generated/api.d.ts", "scripts": {"dev": "convex dev"}, "type": "module", "author": "", "license": "ISC", "dependencies": {"@convex-dev/aggregate": "^0.1.20", "@convex-dev/auth": "^0.0.80", "@convex-dev/migrations": "^0.2.5", "@convex-dev/polar": "^0.4.2", "@convex-dev/react-query": "^0.0.0-alpha.5", "@convex-dev/twilio": "^0.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@react-email/components": "^0.0.15", "@react-email/render": "^0.0.12", "@stream-io/node-sdk": "^0.4.24", "@types/bcryptjs": "^3.0.0", "bcryptjs": "^3.0.2", "convex": "^1.21.0", "convex-helpers": "^0.1.74", "jszip": "^3.10.1", "livekit-server-sdk": "^2.13.0", "loops": "^5.0.1", "openai": "^4.61.1", "react": "^19.0.0", "react-dom": "^19.0.0", "standardwebhooks": "^1.0.0", "zod": "^3.24.2"}}