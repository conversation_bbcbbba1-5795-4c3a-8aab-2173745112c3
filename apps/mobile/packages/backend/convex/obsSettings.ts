import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getCurrentUser } from "./helpers/utils";

export const getForCurrentUser = query({
  args: { streamId: v.id("streams") },
  handler: async (ctx, { streamId }) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");
    return await ctx.db
      .query("obsSettings")
      .withIndex("by_user_stream", (q) => q.eq("userId", user._id).eq("streamId", streamId))
      .first();
  },
});

export const upsertForCurrentUser = mutation({
  args: {
    streamId: v.id("streams"),
    obsStreamKey: v.optional(v.string()),
    obsServerUrl: v.optional(v.string()),
    profileName: v.optional(v.string()),
    ingestRegion: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");
    const { streamId, ...rest } = args;
    const existing = await ctx.db
      .query("obsSettings")
      .withIndex("by_user_stream", (q) => q.eq("userId", user._id).eq("streamId", streamId))
      .first();
    const now = Date.now();
    if (existing) {
      await ctx.db.patch(existing._id, { ...rest, updatedAt: now });
      return existing._id;
    } else {
      return await ctx.db.insert("obsSettings", {
        userId: user._id,
        streamId,
        ...rest,
        updatedAt: now,
      });
    }
  },
});

export const getForUser = query({
  args: { userId: v.id("users"), streamId: v.id("streams") },
  handler: async (ctx, { userId, streamId }) => {
    return await ctx.db
      .query("obsSettings")
      .withIndex("by_user_stream", (q) => q.eq("userId", userId).eq("streamId", streamId))
      .first();
  },
}); 