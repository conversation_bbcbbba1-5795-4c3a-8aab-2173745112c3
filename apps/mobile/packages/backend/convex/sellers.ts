import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getCurrentUser } from "./helpers/utils";
import { Id } from "./_generated/dataModel";
import { CURRENCY_VALIDATOR, PRODUCT_CONDITION, PRODUCT_VISIBILITY } from "./lib/validators";

/**
 * @name becomeSeller
 * @description Convert a regular user to a seller
 */
export const becomeSeller = mutation({
  args: {
    username: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const existingSeller = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("username"), args.username))
      .first();

    if (existingSeller) {
      throw new Error("Store name already taken");
    }

    await ctx.db.patch(user._id, {
      role: "seller",
      username: args.username,

    });
  },
});

/**
 * @name addProduct
 * @description Add a product that can be sold during streams
 */
export const addProduct = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    price: v.number(),
    currency: CURRENCY_VALIDATOR,
    inventory: v.number(),
    category: v.string(),
    condition: PRODUCT_CONDITION,
    images: v.array(v.id("_storage")),
    streamId: v.optional(v.array(v.id("streams"))),
    subcategory: v.optional(v.string()),
    flashSale: v.optional(v.boolean()),
    acceptOffers: v.optional(v.boolean()),
    reserveForLive: v.optional(v.boolean()),
    shippingProfile: v.optional(v.string()),
    hasHazardousMaterials: v.optional(v.boolean()),
    variants: v.optional(
      v.array(
        v.object({
          color: v.optional(v.string()),
          size: v.optional(v.string()),
          quantity: v.optional(v.number()),
        }),
      ),
    ),
    quantity: v.optional(v.number()),
    isAuction: v.optional(v.boolean()),
    startingBid: v.optional(v.number()),
    suddenDeath: v.optional(v.boolean()),
    visibility: PRODUCT_VISIBILITY,
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");
    if (user.role !== "seller")
      throw new Error("Only sellers can add products");

    return await ctx.db.insert("products", {
      sellerId: user._id,
      name: args.name,
      description: args.description,
      price: args.price,
      currency: args.currency,
      inventory: args.inventory,
      category: args.category,
      condition: args.condition,
      images: args.images,
      status: "active",
      tags: [],
      streamId: args.streamId,
      subcategory: args.subcategory,
      flashSale: args.flashSale,
      acceptOffers: args.acceptOffers,
      reserveForLive: args.reserveForLive,
      shippingProfile: args.shippingProfile,
      hasHazardousMaterials: args.hasHazardousMaterials,
      variants: args.variants,
      quantity: args.quantity,
      isAuction: args.isAuction,
      startingBid: args.startingBid,
      suddenDeath: args.suddenDeath,
      visibility: args.visibility,
    });
  },
});

/**
 * @name getSellerProfile
 * @description Get a seller's profile
 */
export const getSellerProfile = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const seller = await ctx.db.get(args.userId);
    if (!seller || seller.role !== "seller") {
      throw new Error("Seller not found");
    }

    const activeStreams = await ctx.db
      .query("streams")
      .filter((q) =>
        q.and(
          q.eq(q.field("hostId"), args.userId),
          q.eq(q.field("status"), "scheduled"),
          q.eq(q.field("status"), "live"),
        ),
      )
      .collect();

    const products = await ctx.db
      .query("products")
      .filter((q) =>
        q.and(
          q.eq(q.field("sellerId"), args.userId),
          q.eq(q.field("status"), "active"),
        ),
      )
      .collect();

    return {
      ...seller,
      activeStreams,
      products,
    };
  },
});

/**
 * @name getActiveStreams
 * @description Get all currently active streams
 */
export const getActiveStreams = query({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const query = ctx.db
      .query("streams")
      .withIndex("by_hostId", (q) => q.eq("hostId", user._id))
      .filter((q) => 
        q.or(
          q.eq(q.field("status"), "scheduled"),
          q.eq(q.field("status"), "live")
        )
      );

    return await query.collect();
  },
});

/**
 * @name getProductsForStream
 * @description Get all products for a given stream
 */
export const getProductsForStream = query({
  args: {
    streamId: v.id("streams"),
  },
  handler: async (ctx, args) => {
    const products = await ctx.db
      .query("products")
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();
      
    return products.filter(
      (product) => Array.isArray(product.streamId) && product.streamId.includes(args.streamId)
    );
  },
});
