import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const get = query({
  args: {
    column: v.string(),
  },
  handler: async (ctx, args) => {
    const preferences = await ctx.db
      .query("columnPreferences")
      .withIndex("by_column", (q) => q.eq("column", args.column))
      .first();

    return preferences;
  },
});

export const update = mutation({
  args: {
    column: v.string(),
    trackTimeInStatus: v.optional(v.boolean()),
    showConfetti: v.optional(v.boolean()),
    hidden: v.optional(v.boolean()),
    targetTimeInStatus: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const { column, ...updates } = args;

    const existing = await ctx.db
      .query("columnPreferences")
      .withIndex("by_column", (q) => q.eq("column", column))
      .first();

    if (existing) {
      return await ctx.db.patch(existing._id, updates);
    } else {
      return await ctx.db.insert("columnPreferences", {
        column,
        trackTimeInStatus: updates.trackTimeInStatus ?? false,
        showConfetti: updates.showConfetti ?? false,
        hidden: updates.hidden ?? false,
        targetTimeInStatus: updates.targetTimeInStatus,
      });
    }
  },
});

export const getAll = query({
  args: {},
  handler: async (ctx) => {
    const preferences = await ctx.db.query("columnPreferences").collect();

    return preferences;
  },
});
