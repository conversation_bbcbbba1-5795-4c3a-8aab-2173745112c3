import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Id } from "./_generated/dataModel";
import { getCurrentUser } from "./helpers/utils";
import { filter } from "convex-helpers/server/filter";

// Create a new chat (single or group)
export const createChat = mutation({
  args: {
    participantIds: v.array(v.id("users")),
    title: v.optional(v.string()),
    isGroup: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);

    if (!currentUser) {
      throw new Error("User not found");
    }

    // For group chats, ensure we have at least 1 other participant
    if (args.isGroup && args.participantIds.length < 1) {
      throw new Error("Group chats require at least 1 other participant");
    }

    // For 1:1 chats, ensure we have exactly 1 other participant
    if (!args.isGroup && args.participantIds.length !== 1) {
      throw new Error("1:1 chats require exactly 1 other participant");
    }

    // Check if a chat already exists between these participants for 1:1 chats
    if (!args.isGroup) {
      const existingChat = await ctx.db
        .query("chats")
        .filter(
          (q) =>
            q.eq(q.field("isGroup"), false) &&
            q.eq(q.field("participants"), [
              currentUser._id,
              args.participantIds[0] as Id<"users">,
            ]),
        )
        .first();

      if (existingChat) {
        return existingChat._id;
      }
    }

    // Create chat with all participants (including current user)
    const chatId = await ctx.db.insert("chats", {
      title: args.title,
      createdBy: currentUser._id,
      participants: [currentUser._id, ...args.participantIds],
      lastMessageAt: Date.now(),
      isGroup: args.isGroup ?? false,
    });

    return chatId;
  },
});

// Send a message in a chat
export const sendMessage = mutation({
  args: {
    chatId: v.id("chats"),
    text: v.string(),
    image: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);

    if (!currentUser) {
      throw new Error("User not found");
    }

    // Verify user is a participant in the chat
    const chat = await ctx.db.get(args.chatId);
    if (!chat || !chat.participants.includes(currentUser._id)) {
      throw new Error("Not authorized to send message in this chat");
    }

    // Create the message
    const messageId = await ctx.db.insert("messages", {
      chatId: args.chatId,
      senderId: currentUser._id,
      text: args.text,
      image: args.image,
    });

    // Update the chat's lastMessageAt
    await ctx.db.patch(args.chatId, {
      lastMessageAt: Date.now(),
    });

    return messageId;
  },
});

// Get all chats for the current user
export const listChats: ReturnType<typeof query> = query({
  handler: async (ctx) => {
    const currentUser = await getCurrentUser(ctx);

    if (!currentUser) {
      throw new Error("User not found");
    }

    // Get all chats for the user
    const chats = await ctx.db
      .query("chats")
      .collect();
    const userChats = chats.filter((chat) => chat.participants.includes(currentUser._id));

    // Sort all chats by last message time
    const sortedChats = userChats.sort(
      (a, b) => b.lastMessageAt - a.lastMessageAt,
    );

    // Get participants and last message for each chat
    const enrichedChats = await Promise.all(
      sortedChats.map(async (chat) => {
        // Get participants with their image URLs
        const participants = await Promise.all(
          chat.participants.map(async (id) => {
            const user = await ctx.db.get(id);
            if (!user) return null;

            // Get image URL if it exists, using the same approach as in users.ts
            let imageUrl = null;
            if (user.image) {
              // Check if it's already a URL
              if (typeof user.image === 'string' && (user.image.startsWith('http://') || user.image.startsWith('https://'))) {
                imageUrl = user.image;
              } else {
                try {
                  // Try to get URL from storage
                  imageUrl = await ctx.storage.getUrl(user.image);
                } catch (error) {
                  console.error(`Error getting image URL for user ${id}:`, error);
                  imageUrl = null;
                }
              }
            }

            return {
              ...user,
              imageUrl,
            };
          }),
        );

        // Get last message
        const messages = await ctx.db
          .query("messages")
          .withIndex("by_chat", (q) => q.eq("chatId", chat._id))
          .order("desc")
          .take(1);

        // Calculate unread count for this chat
        const unreadCount = (await ctx.db
          .query("messages")
          .withIndex("by_chat", (q) => q.eq("chatId", chat._id))
          .collect()
        ).filter((m) => m.senderId !== currentUser._id && !m.readAt).length;

        // Filter out any null participants
        const validParticipants = participants.filter(
          (p): p is NonNullable<typeof p> => p !== null,
        );

        // For 1:1 chats, get the other user
        let otherUser = null;
        if (!chat.isGroup && validParticipants.length === 2) {
          otherUser =
            validParticipants.find((p) => p._id !== currentUser._id) || null;
        }

        return {
          ...chat,
          participants: validParticipants,
          otherUser,
          lastMessage: messages[0] || null,
          unreadCount,
        };
      }),
    );

    return enrichedChats;
  },
});

// Get messages for a specific chat
export const getMessages = query({
  args: {
    chatId: v.id("chats"),
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);

    if (!currentUser) {
      throw new Error("User not found");
    }

    // Check if chat exists
    const chat = await ctx.db.get(args.chatId);

    // If chat doesn't exist, return empty array
    if (!chat) {
      return [];
    }

    // If chat exists, verify user is a participant
    if (!chat.participants.includes(currentUser._id)) {
      throw new Error("Not authorized to view this chat");
    }

    const messages = await ctx.db
      .query("messages")
      .withIndex("by_chat", (q) => q.eq("chatId", args.chatId))
      .order("desc")
      .take(50);

    return messages.reverse();
  },
});

// Get a specific chat's details
export const getChat = query({
  args: {
    chatId: v.id("chats"),
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);

    if (!currentUser) {
      throw new Error("User not found");
    }

    // Check if chat exists
    const chat = await ctx.db.get(args.chatId);

    // If chat doesn't exist, return null
    if (!chat) {
      return null;
    }

    // If chat exists, verify user is a participant
    if (!chat.participants.includes(currentUser._id)) {
      throw new Error("Not authorized to view this chat");
    }

    // Get all participants' details with image URLs
    const participants = await Promise.all(
      chat.participants.map(async (id) => {
        const user = await ctx.db.get(id);
        if (!user) return null;

        // Get image URL if it exists, using the same approach as in users.ts
        let image = null;
        if (user.image) {
          // Check if it's already a URL
          if (typeof user.image === 'string' && (user.image.startsWith('http://') || user.image.startsWith('https://'))) {
            image = user.image;
          } else {
            try {
              // Try to get URL from storage
              image = await ctx.storage.getUrl(user.image);
            } catch (error) {
              console.error(`Error getting image URL for user ${id}:`, error);
              image = null;
            }
          }
        }

        return {
          ...user,
          image, // Use 'image' to match the existing UI expectations
        };
      }),
    );

    // Filter out any null participants
    const validParticipants = participants.filter(
      (p): p is NonNullable<typeof p> => p !== null,
    );

    // For 1:1 chats, get the other user's details
    let otherUser = null;
    if (!chat.isGroup && chat.participants.length === 2) {
      otherUser =
        validParticipants.find((p) => p._id !== currentUser._id) || null;
    }

    return {
      ...chat,
      participants: validParticipants,
      otherUser,
      currentUserId: currentUser._id,
    };
  },
});

// Delete a chat and all its messages
export const deleteChat = mutation({
  args: {
    chatId: v.id("chats"),
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);

    if (!currentUser) {
      throw new Error("User not found");
    }

    // Check if chat exists
    const chat = await ctx.db.get(args.chatId);

    // If chat doesn't exist, consider deletion successful
    if (!chat) {
      return true;
    }

    // If chat exists, verify user is a participant
    if (!chat.participants.includes(currentUser._id)) {
      throw new Error("Not authorized to delete this chat");
    }

    // Delete all messages in the chat
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_chat", (q) => q.eq("chatId", args.chatId))
      .collect();

    for (const message of messages) {
      await ctx.db.delete(message._id);
    }

    // Delete the chat itself
    await ctx.db.delete(args.chatId);

    return true;
  },
});

// Get image URL for a message
export const getMessageImageUrl = query({
  args: {
    storageId: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    if (!args.storageId) return null;
    return await ctx.storage.getUrl(args.storageId);
  },
});

// Mutation: Mark all messages in a chat as read for the current user
export const markMessagesAsRead = mutation({
  args: {
    chatId: v.id("chats"),
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);
    if (!currentUser) throw new Error("User not found");

    // Get all messages in the chat not sent by the current user and not yet read
    const messages = await ctx.db
      .query("messages")
      .withIndex("by_chat", (q) => q.eq("chatId", args.chatId))
      .collect();

    const now = Date.now();
    await Promise.all(
      messages.map(async (msg) => {
        if (msg.senderId !== currentUser._id && !msg.readAt) {
          await ctx.db.patch(msg._id, { readAt: now });
        }
      })
    );
    return true;
  },
});

export const getUnreadMessagesCount = query({
  args: {},
  handler: async (ctx): Promise<number> => {
    const currentUser = await getCurrentUser(ctx);
    if (!currentUser) return 0;

    const chats = await ctx.db
      .query("chats")
      .collect();
    const userChats = chats.filter((chat) => chat.participants.includes(currentUser._id));
    const chatIds = userChats.map((c) => c._id);

    let unreadCount = 0;
    for (const chatId of chatIds) {
      const messages = await ctx.db
        .query("messages")
        .withIndex("by_chat", (q) => q.eq("chatId", chatId))
        .collect();
      unreadCount += messages.filter((m) => m.senderId !== currentUser._id && !m.readAt).length;
    }
    return unreadCount;
  },
});
