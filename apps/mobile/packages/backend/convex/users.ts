import {
  internalQuery,
  query,
  QueryCtx,
  mutation,
  action,
  internalMutation,
  ActionCtx,
  MutationCtx,
} from "./_generated/server";
import { getCurrentUser } from "./helpers/utils.js";
import { v } from "convex/values";
import { Id, Doc } from "./_generated/dataModel";
import { LOGIN_TYPE, username } from "./lib/validators";
import { verifyPassword, hashPassword } from "./helpers/scrypt";
import { internal } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";
import { api } from "./_generated/api";
import { ConvexError } from "convex/values";
import { paginationOptsValidator } from "convex/server";
import { aggregateFollows, aggregateUsers } from "./custom";
import { User } from "./lib/types";
import Stripe from "stripe";
import { identity } from "lodash";

/**
 * @name viewer
 * @description Get the current user
 */
export const viewer = query({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) return null;

    try {
      const authAccounts = await ctx.db
        .query("authAccounts")
        .filter((q) => q.eq(q.field("userId"), user._id))
        .collect();

      const passwordAccount = authAccounts.find(
        (account) => account.provider === "password",
      );
      const hashedPassword = passwordAccount?.secret ? true : false;

      const accounts = authAccounts.map((account) => ({
        provider: account.provider,
      }));

      let avatarUrl: string | undefined = user.image || undefined;

      if (user.image && !user.image.startsWith("http")) {
        try {
          const url = await ctx.storage.getUrl(user.image as Id<"_storage">);
          avatarUrl = url || undefined;
        } catch (error) {
          console.error("Error getting avatar URL:", error);
        }
      }

      let coverImageUrl: string | undefined = user.coverImage || undefined;
      if (user.coverImage && !user.coverImage.startsWith("http")) {
        try {
          const url = await ctx.storage.getUrl(
            user.coverImage as Id<"_storage">,
          );
          coverImageUrl = url || undefined;
        } catch (error) {
          console.error("Error getting cover image URL:", error);
        }
      }

      const followCounts: { followers: number; following: number } = await ctx.runQuery(api.users.getFollowCounts, {
        userId: user._id,
      });

      return {
        ...user,
        avatarUrl,
        coverImageUrl,
        hashedPassword,
        accounts,
        followers: Array(followCounts.followers).fill(null),
        following: Array(followCounts.following).fill(null),
      };
    } catch (error) {
      console.error("Error fetching additional user data:", error);
      return {
        ...user,
        avatarUrl: user.image?.startsWith("http") ? user.image : undefined,
        coverImageUrl: user.coverImage?.startsWith("http")
          ? user.coverImage
          : undefined,
        hashedPassword: false,
        accounts: [],
        followers: [],
        following: [],
      };
    }
  },
});

/**
 * @name updateUsername
 * @description Update a user's username
 */
export const updateUsername = mutation({
  args: {
    username: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) {
      return;
    }
    const validatedUsername = username.safeParse(args.username);

    if (!validatedUsername.success) {
      throw new Error(validatedUsername.error.message);
    }
    await ctx.db.patch(user._id, { username: validatedUsername.data });
  },
});

/**
 * @name update
 * @description Update a user's profile
 * @param {string} name - The name of the user
 * @param {string} firstName - The first name of the user
 * @param {string} lastName - The last name of the user
 * @param {string} email - The email of the user
 * @param {string} phone - The phone of the user
 * @param {string} image - The image of the user
 * @param {number} emailVerificationTime - The email verification time of the user
 * @param {number} phoneVerificationTime - The phone verification time of the user
 * @param {boolean} isAnonymous - Whether the user is anonymous
 * @param {LOGIN_TYPE} lastLoginType - The last login type of the user
 * @param {string} color - The color of the user
 * @param {boolean} subscribedToUpdates - Whether the user is subscribed to updates
 */
export const update = mutation({
  args: {
    id: v.id("users"),
    name: v.optional(v.string()),
    email: v.optional(v.string()),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    image: v.optional(v.string()),
    loginType: v.optional(LOGIN_TYPE),
    username: v.optional(v.string()),
    phone: v.optional(v.string()),
    finishedSignUp: v.optional(v.boolean()),
    isSellerInterested: v.optional(v.boolean()),
    bio: v.optional(v.string()),
    coverImage: v.optional(v.id("_storage")),
    role: v.optional(v.union(v.literal("user"), v.literal("seller"), v.literal("admin"))),
    color: v.optional(v.string()),
    preferences: v.optional(
      v.object({
        categories: v.optional(v.array(v.string())),
        subcategories: v.optional(v.array(v.string())),
        notifications: v.optional(v.boolean()),
        emailUpdates: v.optional(v.boolean()),
        darkMode: v.optional(v.boolean()),
        shippingAddresses: v.optional(
          v.array(
            v.object({
              street: v.string(),
              address2: v.optional(v.string()),
              fullName: v.optional(v.string()),
              city: v.string(),
              state: v.string(),
              country: v.string(),
              zipCode: v.string(),
              isDefault: v.optional(v.boolean()),
              isReturn: v.optional(v.boolean()),
            }),
          ),
        ),
      }),
    ),
  },
  handler: async (ctx, args) => {
    const { id, ...updateFields } = args;
    if (!id) throw new Error("User ID is required");

    const oldUser = await ctx.db.get(id);
    if (!oldUser) throw new Error("User not found");

    const currentUser = await getCurrentUser(ctx);
    const adminId = currentUser?._id ?? "system";

    await ctx.db.patch(id, { ...updateFields });

    const newUser = await ctx.db.get(id);
    if (!newUser) return;

    const changedFields: { field: string; oldValue: any; newValue: any }[] = [];
    for (const key of Object.keys(updateFields)) {
      const oldValue = (oldUser as any)[key];
      const newValue = (newUser as any)[key];
      const isObject = typeof oldValue === "object" && typeof newValue === "object";
      const isDifferent = isObject
        ? JSON.stringify(oldValue) !== JSON.stringify(newValue)
        : oldValue !== newValue;
      if (isDifferent) {
        changedFields.push({ field: key, oldValue, newValue });
      }
    }

    if (changedFields.some(change => change.field === "lastName" || change.field === "firstName")) {
      const newName = `${newUser.firstName} ${newUser.lastName}`;
      await ctx.db.patch(id, { name: newName });
    }

    if (changedFields.length > 0) {
      const changesDescription = changedFields
        .map(
          (change) =>
            `Field '${change.field}' changed from '${JSON.stringify(change.oldValue)}' to '${JSON.stringify(change.newValue)}'`
        )
        .join("; ");
      await ctx.db.insert("activities", {
        type: "user_updated",
        description: changesDescription,
        createdBy: adminId as Id<"users">,
        metadata: {
          objectId: id,
        },
      });
    }
    
    return {
      updatedBy: adminId,
      updater: adminId !== "system" ? currentUser : null,
    };
  },
});

/**
 * @name getUserLoginType
 * @description Get a user's last login type by email
 * @param {string} email - The email of the user
 * @returns {Promise<string | null>} The last login type of the user
 */
export const getUserLoginType = query({
  args: { email: v.string() },
  handler: async (ctx: QueryCtx, args: { email: string }) => {
    const user = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("email"), args.email.toLowerCase().trim()))
      .first();

    const authAccount = await ctx.db
      .query("authAccounts")
      .filter((q) =>
        q.and(
          q.eq(q.field("providerAccountId"), args.email.toLowerCase().trim()),
          q.eq(q.field("provider"), "password"),
        ),
      )
      .first();

    if (authAccount && !user) {
      return "password";
    }

    return user?.lastLoginType || null;
  },
});

/**
 * @name deleteAccount
 * @description Delete a user's account
 * @param {Id<"users">} id - The ID of the user to delete
 * @returns {Promise<void>}
 */
export const deleteAccount = mutation({
  args: {
    id: v.id("users"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    if (user.image) {
      const storageId = user.image.split("/").pop();
      if (storageId) {
        await ctx.storage.delete(storageId as Id<"_storage">);
      }
    }

    await ctx.db.delete(user._id);
  },
});

/**
 * @name updatePassword
 * @description Update a user's password
 * @param {string} oldPassword - The current password (if exists)
 * @param {string} newPassword - The new password to set
 * @returns {Promise<void>}
 */
export const updatePassword = mutation({
  args: {
    oldPassword: v.optional(v.string()),
    newPassword: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("User not found");

    const authAccount = await ctx.db
      .query("authAccounts")
      .filter((q) =>
        q.and(
          q.eq(q.field("userId"), user._id),
          q.eq(q.field("provider"), "password"),
        ),
      )
      .first();

    if (authAccount?.secret) {
      if (!args.oldPassword) {
        throw new Error("Current password is required");
      }
      const isValid = await verifyPassword(
        args.oldPassword,
        authAccount.secret,
      );
      if (!isValid) {
        throw new Error("Current password is incorrect");
      }
    }

    const hashedPassword = await hashPassword(args.newPassword);

    if (authAccount) {
      await ctx.db.patch(authAccount._id, { secret: hashedPassword });
    } else {
      await ctx.db.insert("authAccounts", {
        userId: user._id,
        provider: "password",
        providerAccountId: user.email,
        secret: hashedPassword,
      });
    }
  },
});

/**
 * @name updateEmail
 * @description Update a user's email address and handle auth accounts
 * @param {string} newEmail - The new email address
 * @returns {Promise<void>}
 */
export const updateEmail = mutation({
  args: {
    newEmail: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("User not found");

    const newEmail = args.newEmail.toLowerCase().trim();

    const existingUser = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("email"), newEmail))
      .first();

    if (existingUser && existingUser._id !== user._id) {
      throw new Error("Email is already taken");
    }

    const authAccounts = await ctx.db
      .query("authAccounts")
      .filter((q) => q.eq(q.field("userId"), user._id))
      .collect();

    for (const account of authAccounts) {
      if (account.provider === "password") {
        await ctx.db.patch(account._id, {
          providerAccountId: newEmail,
        });
      } else if (account.provider === "google") {
        await ctx.db.delete(account._id);
      }
    }

    await ctx.db.patch(user._id, {
      email: newEmail,
      lastLoginType: "password",
    });
  },
});

/**
 * @name updateUserLoginType
 * @description Update a user's login type
 * @param {string} email - The email of the user
 * @param {LOGIN_TYPE} loginType - The login type of the user
 * @returns {Promise<void>}
 */
export const updateUserLoginType = mutation({
  args: {
    email: v.string(),
    loginType: LOGIN_TYPE,
  },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("email"), args.email))
      .first();

    if (!user) throw new Error("User not found");

    await ctx.db.patch(user._id, { lastLoginType: args.loginType });
  },
});

/**
 * @name updatePresence
 * @description Update the user's presence status
 */
export const updatePresence = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    await ctx.db.patch(user._id, {
      lastSeen: Date.now(),
      status: "online",
    });
  },
});

/**
 * @name markAsIdle
 * @description Mark the user as idle
 */
export const markAsIdle = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    await ctx.db.patch(user._id, {
      lastSeen: Date.now(),
      status: "idle",
    });
  },
});

/**
 * @name markAsOffline
 * @description Mark the user as offline
 */
export const markAsOffline = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    await ctx.db.patch(user._id, {
      lastSeen: Date.now(),
      status: "offline",
    });
  },
});

/**
 * @name list
 * @description List users by their IDs
 * @param {Id<"users">[]} userIds - The IDs of the users to list
 * @returns {Promise<User[]>} The users
 */
export const list = query({
  args: { userIds: v.array(v.id("users")) },
  handler: async (ctx, { userIds }) => {
    const users = await Promise.all(userIds.map((id) => ctx.db.get(id)));
    return users.filter(
      (user): user is NonNullable<typeof user> => user !== null,
    );
  },
});

/**
 * @INTERNAL
 * @name INTERNAL_getUser
 * @description Get the current user
 */
export const INTERNAL_getUser = internalQuery({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("User not found");

    return user;
  },
});

export const INTERNAL_getUserFromId = internalQuery({
  args: {},
  handler: async (ctx) => {
    const userId = (await getAuthUserId(ctx)) as Id<"users">;
    if (!userId) return null;
    return await ctx.db.get(userId);
  },
});

/**
 * @name sendVerificationCode
 * @description Send a verification code to a user's phone number
 * @param {string} phone - The phone number of the user
 * @returns {Promise<void>}
 */
export const sendVerificationCode = action({
  args: {
    phone: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.runQuery(internal.users.INTERNAL_getUserFromId);
    if (!user) throw new Error("Not authenticated");

    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    if (!phoneRegex.test(args.phone)) {
      throw new Error(
        "Invalid phone number format. Please use E.164 format (e.g., +1234567890)",
      );
    }

    const verificationCode = Math.floor(
      100000 + Math.random() * 900000,
    ).toString();

    try {
      await ctx.runAction(internal.sms.sendSms, {
        to: args.phone,
        body: `Your Liveciety verification code is ${verificationCode}`,
      });

      const newVerificationCode = await ctx.runMutation(
        internal.users.storeVerificationCode,
        {
          userId: user._id,
          code: verificationCode,
          phone: args.phone,
          expiresAt: Date.now() + 10 * 60 * 1000, // 10 minutes
        },
      );

      await ctx.scheduler.runAfter(
        10 * 60 * 1000,
        internal.sms.deleteVerificationCode,
        {
          userId: user._id,
          codeId: newVerificationCode as Id<"verificationCodes">,
        },
      );

      return true;
    } catch (error) {
      console.error("Error sending verification code:", error);
      throw new Error("Failed to send verification code");
    }
  },
});

export const storeVerificationCode = internalMutation({
  args: {
    userId: v.id("users"),
    code: v.string(),
    phone: v.string(),
    expiresAt: v.number(),
  },
  handler: async (ctx: MutationCtx, args) => {
    const existing = await ctx.db
      .query("verificationCodes")
      .filter((q) =>
        q.and(
          q.eq(q.field("userId"), args.userId),
          q.or(
            q.lt(q.field("expiresAt"), Date.now()),
            q.eq(q.field("verified"), false),
          ),
        ),
      )
      .collect();

    for (const code of existing) {
      await ctx.db.delete(code._id);
    }

    const id = await ctx.db.insert("verificationCodes", {
      userId: args.userId,
      code: args.code,
      phone: args.phone,
      expiresAt: args.expiresAt,
      verified: false,
      attempts: 0,
    });

    return id;
  },
});

export const verifyCode = mutation({
  args: {
    code: v.string(),
    phone: v.string(),
  },
  handler: async (ctx: MutationCtx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const verificationCode = await ctx.db
      .query("verificationCodes")
      .filter((q) =>
        q.and(
          q.eq(q.field("userId"), user._id),
          q.eq(q.field("phone"), args.phone),
          q.eq(q.field("verified"), false),
        ),
      )
      .order("desc")
      .first();

    if (!verificationCode) {
      throw new Error("Verification code not found");
    }

    if (verificationCode.expiresAt < Date.now()) {
      throw new Error("Verification code has expired");
    }

    await ctx.db.patch(verificationCode._id, {
      attempts: (verificationCode.attempts || 0) + 1,
    });

    if ((verificationCode.attempts || 0) >= 3) {
      await ctx.db.delete(verificationCode._id);
      throw new Error("Too many attempts. Please request a new code.");
    }

    if (verificationCode.code !== args.code) {
      throw new Error("Invalid verification code");
    }

    await ctx.db.patch(verificationCode._id, { verified: true });
    await ctx.db.patch(user._id, {
      phone: args.phone,
      phoneVerificationTime: Date.now(),
    });

    return true;
  },
});

/**
 * @name generateUsername
 * @description Generate username suggestions using OpenAI
 */
export const generateUsername = action({
  args: {
    name: v.string(),
  },
  handler: async (ctx, args) => {
    const suggestions: any = await ctx.runAction(
      api.integration.openai.generateUsernameSuggestions,
      {
        name: args.name,
      },
    );

    return suggestions;
  },
});

/**
 * @name checkUsernameAvailable
 * @description Check if a username is available
 */
export const checkUsernameAvailable = query({
  args: {
    username: v.string(),
  },
  handler: async (ctx, args) => {
    const existingUser = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("username"), args.username.toLowerCase()))
      .first();

    return !existingUser;
  },
});

/**
 * @name hasActiveVerificationCode
 * @description Check if a user has an active verification code
 */
export const hasActiveVerificationCode = query({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) return false;

    const activeCode = await ctx.db
      .query("verificationCodes")
      .filter((q) =>
        q.and(
          q.eq(q.field("userId"), user._id),
          q.gt(q.field("expiresAt"), Date.now()),
          q.eq(q.field("verified"), false),
        ),
      )
      .first();

    const verifiedCode = await ctx.db
      .query("verificationCodes")
      .filter((q) =>
        q.and(
          q.eq(q.field("userId"), user._id),
          q.eq(q.field("verified"), true),
        ),
      )
      .first();

    return activeCode !== null || verifiedCode !== null;
  },
});

export const updateProfile = mutation({
  args: {
    name: v.string(),
    username: v.string(),
    bio: v.optional(v.string()),
    image: v.optional(v.id("_storage")),
    coverImage: v.optional(v.id("_storage")),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new ConvexError("you must be logged in to update your profile");
    }

    const existingUser = await ctx.db
      .query("users")
      .filter((q) =>
        q.and(
          q.eq(q.field("username"), args.username),
          q.neq(q.field("_id"), user._id),
        ),
      )
      .first();

    if (existingUser) {
      throw new ConvexError("username is already taken");
    }

    if (user.image && user.image !== args.image) {
      await ctx.storage.delete(user.image as Id<"_storage">);
    }
    if (user.coverImage && user.coverImage !== args.coverImage) {
      await ctx.storage.delete(user.coverImage as Id<"_storage">);
    }

    const updateData = {
      name: args.name,
      username: args.username,
      ...(args.bio !== undefined && { bio: args.bio }),
      ...(args.image !== undefined && { image: args.image }),
      ...(args.coverImage !== undefined && { coverImage: args.coverImage }),
    };

    await ctx.db.patch(user._id, updateData);

    return { success: true };
  },
});

export const getImageUrl = query({
  args: { storageId: v.optional(v.id("_storage")) },
  handler: async (ctx, { storageId }) => {
    if (!storageId) return null;
    return await ctx.storage.getUrl(storageId);
  },
});

const userSearchIndexMap = {
  name: "search_name",
  email: "search_email",
  username: "search_users",
} as const;
type UserSearchIndexValue = (typeof userSearchIndexMap)[keyof typeof userSearchIndexMap];

function isUserSearchIndexValue(val: string): val is UserSearchIndexValue {
  return ["search_name", "search_email", "search_users"].includes(val);
}

type UserSearchField = keyof typeof userSearchIndexMap;

function isUserSearchField(val: string): val is UserSearchField {
  return ["name", "email", "username"].includes(val);
}

export const searchUsers = query({
  args: {
    searchQuery: v.optional(v.string()),
    paginationOpts: v.optional(paginationOptsValidator),
    filters: v.optional(v.any()),
    sortOrder: v.optional(v.union(v.literal("asc"), v.literal("desc"))),
  },
  async handler(ctx, args): Promise<{ users: any[]; continueCursor: string | null; isDone: boolean; }> {
    const { searchQuery = "", paginationOpts, sortOrder = "asc" } = args;
    const trimmedQuery = searchQuery.toLowerCase().trim();
    const currentUser = await getCurrentUser(ctx);

    let usersQuery: any = ctx.db.query("users");
    let usedSearchIndex = false;

    let indexName: string | undefined;
    let searchField: string | undefined;
    let searchValue: string | undefined;

    if (searchQuery && searchQuery.trim() !== "") {
      const usernameResults: Doc<"users">[] = await ctx.db
        .query("users")
        .withSearchIndex("search_users", (q: any) =>
          q.search("username", trimmedQuery)
        )
        .take(10);

      const nameResults: Doc<"users">[] = await ctx.db
        .query("users")
        .withSearchIndex("search_name", (q: any) =>
          q.search("name", trimmedQuery)
        )
        .take(10);

      const usersMap = new Map<string, Doc<"users">>();
      [...usernameResults, ...nameResults].forEach((user) => {
        usersMap.set(user._id, user);
      });
      const users: Doc<"users">[] = Array.from(usersMap.values());

      const usersWithSelectedFields: any[] = await Promise.all(
        users.map(async (user: Doc<"users">) => {
          try {
            const followCounts: any = await ctx.runQuery(api.users.getFollowCounts, { userId: user._id });
            return {
              _id: user._id,
              _creationTime: user._creationTime,
              username: user.username,
              name: user.name,
              role: user.role,
              color: user.color,
              image: user.image,
              coverImage: user.coverImage,
              email: user.email,
              followersCount: followCounts.followers,
            };
          } catch (error) {
            return {
              _id: user._id,
              _creationTime: user._creationTime,
              username: user.username,
              name: user.name,
              role: user.role,
              email: user.email,
              color: user.color,
              image: user.image,
              coverImage: user.coverImage,
              followersCount: 0,
            };
          }
        })
      );

      return {
        users: usersWithSelectedFields,
        continueCursor: null,
        isDone: true,
      };
    }

    if (searchQuery && searchQuery.trim() !== "") {
      indexName = "search_name";
      searchField = "name";
      searchValue = searchQuery.trim().toLowerCase();
      usedSearchIndex = true;
    } else if (args.filters && Array.isArray(args.filters)) {
      const searchFilter = args.filters.find(
        (filter: any) => filter.operator === "iLike" && ["name", "email", "username"].includes(filter.id)
      );
      if (searchFilter) {
        indexName = userSearchIndexMap[searchFilter.id as keyof typeof userSearchIndexMap];
        searchField = searchFilter.id;
        searchValue = searchFilter.value;
        usedSearchIndex = true;
      }
    }

    if (usedSearchIndex && indexName && searchField && searchValue && isUserSearchIndexValue(indexName) && isUserSearchField(searchField)) {
      usersQuery = usersQuery.withSearchIndex(indexName, (q: any) =>
        q.search(searchField, searchValue)
      );
    }

    if (args.filters && Array.isArray(args.filters)) {
      for (const filter of args.filters) {
        if (
          filter.value !== undefined &&
          filter.value !== "" &&
          !(usedSearchIndex && filter.operator === "iLike" && ["name", "email", "username"].includes(filter.id))
        ) {
          if (filter.operator === "eq") {
            usersQuery = usersQuery.filter((q: any) =>
              q.eq(q.field(filter.id), filter.value)
            );
          }
        }
      }
    }

    if (!usedSearchIndex) {
      usersQuery = usersQuery.order(sortOrder);
    }

    try {
      const paginationOpts = args.paginationOpts || { numItems: 25, cursor: null };
      const { page: users, continueCursor, isDone } = await usersQuery.paginate(paginationOpts);

      if (users.length === 0 && paginationOpts.cursor === null) {
        
        let fallbackQuery: any;
        if (usedSearchIndex && indexName && searchField && searchValue && isUserSearchIndexValue(indexName) && isUserSearchField(searchField)) {
          fallbackQuery = ctx.db.query("users").withSearchIndex(indexName, (q: any) =>
            q.search(searchField, searchValue)
          );
        } else {
          fallbackQuery = ctx.db.query("users");
        }
        if (args.filters && Array.isArray(args.filters)) {
          for (const filter of args.filters) {
            if (
              filter.value !== undefined &&
              filter.value !== "" &&
              !(usedSearchIndex && filter.operator === "iLike" && ["name", "email", "username"].includes(filter.id))
            ) {
              if (filter.operator === "eq") {
                fallbackQuery = fallbackQuery.filter((q: any) =>
                  q.eq(q.field(filter.id), filter.value)
                );
              }
            }
          }
        }
        
        const allUsers = await fallbackQuery.collect();
        const paginatedUsers = allUsers.slice(0, paginationOpts.numItems || 25);
        
        if (paginatedUsers.length > 0) {
          
          const fallbackUsersWithCounts = await Promise.all(
            paginatedUsers.map(async (user: Doc<"users">) => {
              const followCounts = await ctx.runQuery(api.users.getFollowCounts, { userId: user._id });
              return {
                _id: user._id,
                _creationTime: user._creationTime,
                username: user.username,
                name: user.name,
                role: user.role,
                color: user.color,
                image: user.image,
                coverImage: user.coverImage,
                email: user.email,
                followersCount: followCounts.followers,
              };
            })
          );
          
          const hasMore = allUsers.length > paginatedUsers.length;
          
          const nextCursor = hasMore ? String(paginatedUsers.length) : null;
          
          return {
            users: fallbackUsersWithCounts,
            continueCursor: nextCursor,
            isDone: !hasMore,
          };
        }
      }

      let followCounts: any;

      const usersWithSelectedFields = await Promise.all(
        users.map(async (user: Doc<"users">) => {
          try {
            followCounts = await ctx.runQuery(api.users.getFollowCounts, { userId: user._id });
            return {
              _id: user._id,
              _creationTime: user._creationTime,
              username: user.username,
              name: user.name,
              role: user.role,
              color: user.color,
              image: user.image,
              coverImage: user.coverImage,
              email: user.email,
              followersCount: followCounts.followers,
            };
          } catch (error) {
            console.error(`Error getting follow counts for user ${user._id}:`, error);
            return {
              _id: user._id,
              _creationTime: user._creationTime,
              username: user.username,
              name: user.name,
              role: user.role,
              email: user.email,
              color: user.color,
              image: user.image,
              coverImage: user.coverImage,
              followersCount: followCounts.followers,
            };
          }
        })
      );

      return {
        users: usersWithSelectedFields,
        continueCursor,
        isDone,
      };
    } catch (error) {
      console.error("Error in searchUsers:", error);
      
      return {
        users: [],
        continueCursor: null,
        isDone: true,
      };
    }
  },
});

export const getUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);
    const user = await ctx.db.get(args.userId);
    if (!user) return null;

    if (currentUser) {
      const isBlocked = await ctx.db
        .query("blocks")
        .filter((q) =>
          q.and(
            q.eq(q.field("blockerId"), args.userId),
            q.eq(q.field("blockedId"), currentUser._id),
          ),
        )
        .first();

      if (isBlocked) {
        return null;
      }
    }

    const avatarUrl = user.image ? await ctx.storage.getUrl(user.image) : null;
    const coverImageUrl = user.coverImage
      ? await ctx.storage.getUrl(user.coverImage)
      : null;

    return {
      ...user,
      avatarUrl,
      coverImageUrl,
    };
  },
});

/**
 * @name followUser
 * @description Follow a user
 */
export const followUser = mutation({
  args: { targetUserId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    if (user._id === args.targetUserId) {
      throw new Error("Cannot follow yourself");
    }

    const targetUser = await ctx.db.get(args.targetUserId);
    if (!targetUser) {
      throw new Error("Target user not found");
    }

    let followRelationship = await ctx.db
      .query("follows")
      .withIndex("unique_follow", (q) => 
        q.eq("followerId", user._id)
         .eq("followingId", args.targetUserId)
      )
      .first();
    
    if (!followRelationship) {
      await ctx.db.insert("follows", {
        followerId: user._id,
        followingId: args.targetUserId,
      });

      await ctx.db.insert("notifications", {
        userId: args.targetUserId,
        type: "follow",
        actorId: user._id,
        read: false,
      });
    }
    
    return { success: true };
  },
});

/**
 * @name unfollowUser
 * @description Unfollow a user
 */
export const unfollowUser = mutation({
  args: { targetUserId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const follow = await ctx.db
      .query("follows")
      .withIndex("unique_follow", (q) => 
        q.eq("followerId", user._id)
         .eq("followingId", args.targetUserId)
      )
      .first();

    if (follow) {
      await ctx.db.delete(follow._id);
    }

    return { success: true };
  },
});

/**
 * @name isFollowing
 * @description Check if the current user is following another user
 */
export const isFollowing = query({
  args: { targetUserId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) return false;

    const follow = await ctx.db
      .query("follows")
      .withIndex("unique_follow", (q) => 
        q.eq("followerId", user._id)
         .eq("followingId", args.targetUserId)
      )
      .first();

    return !!follow;
  },
});

export const updateSafetyAgreement = mutation({
  args: {},
  handler: async (ctx) => {
    const currentUser = await getCurrentUser(ctx);

    if (!currentUser) throw new Error("User not found");

    await ctx.db.patch(currentUser._id, {
      safetyGuidelinesAgreement: {
        agreed: true,
        agreedAt: Date.now(),
      },
    });

    return true;
  },
});

/**
 * @name blockUser
 * @description Block a user
 */
export const blockUser = mutation({
  args: { targetUserId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    if (user._id === args.targetUserId) {
      throw new Error("Cannot block yourself");
    }

    const existingBlock = await ctx.db
      .query("blocks")
      .filter((q) =>
        q.and(
          q.eq(q.field("blockerId"), user._id),
          q.eq(q.field("blockedId"), args.targetUserId),
        ),
      )
      .first();

    if (existingBlock) {
      throw new Error("User is already blocked");
    }

    await ctx.db.insert("blocks", {
      blockerId: user._id,
      blockedId: args.targetUserId,
    });

    const follow = await ctx.db
      .query("follows")
      .filter((q) =>
        q.and(
          q.eq(q.field("followerId"), user._id),
          q.eq(q.field("followingId"), args.targetUserId),
        ),
      )
      .first();

    if (follow) {
      await ctx.db.delete(follow._id);
    }

    return { success: true };
  },
});

/**
 * @name unblockUser
 * @description Unblock a user
 */
export const unblockUser = mutation({
  args: { targetUserId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const block = await ctx.db
      .query("blocks")
      .filter((q) =>
        q.and(
          q.eq(q.field("blockerId"), user._id),
          q.eq(q.field("blockedId"), args.targetUserId),
        ),
      )
      .first();

    if (block) {
      await ctx.db.delete(block._id);
    }

    return { success: true };
  },
});

/**
 * @name isBlocked
 * @description Check if a user is blocked
 */
export const isBlocked = query({
  args: { targetUserId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) return false;

    const block = await ctx.db
      .query("blocks")
      .filter((q) =>
        q.and(
          q.eq(q.field("blockerId"), args.targetUserId),
          q.eq(q.field("blockedId"), user._id),
        ),
      )
      .first();

    return !!block;
  },
});

/**
 * @name hasBlocked
 * @description Check if the current user has blocked another user
 */
export const hasBlocked = query({
  args: { targetUserId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) return false;

    const block = await ctx.db
      .query("blocks")
      .filter((q) =>
        q.and(
          q.eq(q.field("blockerId"), user._id),
          q.eq(q.field("blockedId"), args.targetUserId),
        ),
      )
      .first();

    return !!block;
  },
});

/**
 * @name getFollowers
 * @description Get a list of users who follow the specified user
 */
export const getFollowers = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const follows = await ctx.db
      .query("follows")
      .filter((q) => q.eq(q.field("followingId"), args.userId))
      .collect();

    const followerIds = follows.map((follow) => follow.followerId);
    const followers = await Promise.all(
      followerIds.map(async (id) => {
        const user = await ctx.db.get(id);
        if (!user) return null;

        let avatarUrl = null;
        if (user.image) {
          if (typeof user.image === 'string' && (user.image.startsWith('http://') || user.image.startsWith('https://'))) {
            avatarUrl = user.image;
          } else {
            try {
              avatarUrl = await ctx.storage.getUrl(user.image);
            } catch (error) {
              console.error(`Error getting avatar URL for user ${id}:`, error);
              avatarUrl = null;
            }
          }
        }

        const currentUser = await getCurrentUser(ctx);
        let isFollowing = false;
        if (currentUser) {
          const followBack = await ctx.db
            .query("follows")
            .filter((q) =>
              q.and(
                q.eq(q.field("followerId"), currentUser._id),
                q.eq(q.field("followingId"), id),
              ),
            )
            .first();
          isFollowing = !!followBack;
        }

        return user
          ? {
              ...user,
              avatarUrl,
              isFollowing,
            }
          : null;
      }),
    );

    return followers.filter(
      (user): user is NonNullable<typeof user> => user !== null,
    );
  },
});

/**
 * @name getFollowing
 * @description Get a list of users that the specified user follows with pagination
 * @param {Id<"users">} userId - The ID of the user whose followed accounts to get
 * @param {object} paginationOpts - Optional pagination parameters
 * @returns {Promise<{users: User[], cursor: string | null, isDone: boolean}>} Paginated list of users
 */
export const getFollowing = query({
  args: {
    userId: v.id("users"),
    paginationOpts: v.optional(paginationOptsValidator)
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);
    const { userId, paginationOpts } = args;

    const followsQuery = ctx.db
      .query("follows")
      .withIndex("by_follower", (q) => q.eq("followerId", userId))
      .order("desc");

    const MAX_ITEMS_PER_PAGE = 50;
    let currentNumItems = args.paginationOpts?.numItems ?? 20;

    if (currentNumItems > MAX_ITEMS_PER_PAGE) {
      console.warn(
        `Client requested ${currentNumItems} items for getFollowing for userId '${userId}', capping to ${MAX_ITEMS_PER_PAGE}. Cursor: ${args.paginationOpts?.cursor}`
      );
      currentNumItems = MAX_ITEMS_PER_PAGE;
    }

    const resolvedPaginationOpts = {
      numItems: currentNumItems,
      cursor: args.paginationOpts?.cursor || null,
    };

    const paginationResult = await followsQuery.paginate(resolvedPaginationOpts);
    const { page: follows, isDone, continueCursor } = paginationResult;

    if (follows.length === 0) {
      return {
        users: [],
        isDone: isDone,
        cursor: continueCursor
      };
    }

    const batchSize = 10;
    const followingIds = follows.map(follow => follow.followingId);
    const following = [];

    for (let i = 0; i < followingIds.length; i += batchSize) {
      const batchIds = followingIds.slice(i, i + batchSize);
      const batchUsers = await Promise.all(
        batchIds.map(async (id) => {
          const user = await ctx.db.get(id);
          if (!user) return null;

          let avatarUrl = null;
          if (user.image) {
            if (typeof user.image === 'string' && (user.image.startsWith('http://') || user.image.startsWith('https://'))) {
              avatarUrl = user.image;
            } else {
              try {
                avatarUrl = await ctx.storage.getUrl(user.image as Id<"_storage">);
              } catch (error) {
                console.error(`Error getting avatar URL for user ${id}:`, error);
                avatarUrl = null;
              }
            }
          }

          let isFollowing = false;
          if (currentUser && currentUser._id !== id) {
            const followRelation = await ctx.db
              .query("follows")
              .withIndex("unique_follow", q => q.eq("followerId", currentUser._id).eq("followingId", id))
              .first();
            isFollowing = !!followRelation;
          }

          return user ? {
            ...user,
            avatarUrl,
            isFollowing,
          } : null;
        })
      );

      following.push(...batchUsers.filter(
        (userEntry): userEntry is NonNullable<typeof userEntry> => userEntry !== null
      ));
    }

    return {
      users: following,
      isDone: isDone,
      cursor: continueCursor
    };
  },
});

/**
 * @name getMutualFollowers
 * @description Get users that both the current user and target user follow
 */
export const getMutualFollowers = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);
    if (!currentUser) return [];

    const targetFollowing = await ctx.db
      .query("follows")
      .filter((q) => q.eq(q.field("followerId"), args.userId))
      .collect();

    const currentFollowing = await ctx.db
      .query("follows")
      .filter((q) => q.eq(q.field("followerId"), currentUser._id))
      .collect();

    const targetFollowingIds = new Set(
      targetFollowing.map((f) => f.followingId),
    );
    const mutualIds = currentFollowing
      .map((f) => f.followingId)
      .filter((id) => targetFollowingIds.has(id));

    const mutualUsers = await Promise.all(
      mutualIds.map(async (id) => {
        const user = await ctx.db.get(id);
        if (!user) return null;

        let avatarUrl = null;
        if (user.image) {
          if (typeof user.image === 'string' && (user.image.startsWith('http://') || user.image.startsWith('https://'))) {
            avatarUrl = user.image;
          } else {
            try {
              avatarUrl = await ctx.storage.getUrl(user.image);
            } catch (error) {
              console.error(`Error getting avatar URL for user ${id}:`, error);
              avatarUrl = null;
            }
          }
        }

        return user
          ? {
              ...user,
              avatarUrl,
              isFollowing: true,
            }
          : null;
      }),
    );

    return mutualUsers.filter(
      (user): user is NonNullable<typeof user> => user !== null,
    );
  },
});

/**
 * @name getNotifications
 * @description Get notifications for the current user, with pagination
 * @param {object} paginationOpts - Optional pagination parameters
 * @returns {Promise<{page: any[], isDone: boolean, continueCursor: string | null}>} Paginated list of notifications with actor details
 */
export const getNotifications = query({
  args: { paginationOpts: v.optional(paginationOptsValidator) },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) {
      return {
        page: [],
        isDone: true,
        continueCursor: null,
      };
    }

    const notificationsQuery = ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .order("desc");

    const MAX_NOTIFICATIONS_PER_PAGE = 25;
    let currentNumItems = args.paginationOpts?.numItems ?? 15;

    if (currentNumItems > MAX_NOTIFICATIONS_PER_PAGE) {
      console.warn(
        `Client requested ${currentNumItems} items for getNotifications for userId '${user._id}', capping to ${MAX_NOTIFICATIONS_PER_PAGE}. Cursor: ${args.paginationOpts?.cursor}`
      );
      currentNumItems = MAX_NOTIFICATIONS_PER_PAGE;
    }

    const resolvedPaginationOpts = {
      numItems: currentNumItems,
      cursor: args.paginationOpts?.cursor || null,
    };

    const paginatedNotifications = await notificationsQuery.paginate(resolvedPaginationOpts);

    if (paginatedNotifications.page.length === 0) {
      return {
        page: [],
        isDone: paginatedNotifications.isDone,
        continueCursor: paginatedNotifications.continueCursor,
      };
    }

    const batchSize = 10; 
    const enrichedNotifications = [];

    for (let i = 0; i < paginatedNotifications.page.length; i += batchSize) {
      const batchOfNotifications = paginatedNotifications.page.slice(i, i + batchSize);
      const batchDetails = await Promise.all(
        batchOfNotifications.map(async (notification) => {
          const actor = await ctx.db.get(notification.actorId as Id<"users"> || notification.userId as Id<"users">);
          if (!actor) return null;

          let avatarUrl = null;
          if (actor.image) {
            if (typeof actor.image === 'string' && (actor.image.startsWith('http://') || actor.image.startsWith('https://'))) {
              avatarUrl = actor.image;
            } else {
              try {
                avatarUrl = await ctx.storage.getUrl(actor.image as Id<"_storage">);
              } catch (error) {
                console.error(`Error getting avatar URL for actor ${actor._id} in notification ${notification._id}:`, error);
                avatarUrl = null;
              }
            }
          }

          let isFollowing = false;
          if (user._id !== actor._id) {
            const followRelation = await ctx.db
              .query("follows")
              .withIndex("unique_follow", q => q.eq("followerId", user._id).eq("followingId", actor._id))
              .first();
            isFollowing = !!followRelation;
          }

          return {
            ...notification,
            actor: {
              _id: actor._id,
              username: actor.username,
              name: actor.name,
              image: actor.image,
              avatarUrl,
              isFollowing,
            },
          };
        }),
      );
      enrichedNotifications.push(...batchDetails.filter(Boolean));
    }

    return {
        page: enrichedNotifications,
        isDone: paginatedNotifications.isDone,
        continueCursor: paginatedNotifications.continueCursor,
    };
  },
});

/**
 * @name getUnreadNotificationsCount
 * @description Get the count of unread notifications for the current user
 */
export const getUnreadNotificationsCount = query({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) return 0;

    const unreadNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("read"), false))
      .collect();

    return unreadNotifications.length || 0;
  },
});

/**
 * @name markNotificationsAsRead
 * @description Mark all notifications as read for the current user
 */
export const markNotificationsAsRead = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const unreadNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("read"), false))
      .collect();

    await Promise.all(
      unreadNotifications.map((notification) =>
        ctx.db.patch(notification._id, { read: true }),
      ),
    );

    return unreadNotifications.length;
  },
});

export const getUserById = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await ctx.db.get(args.userId);
    return user;
  },
});

/**
 * @name createUserReview
 * @description Create a review for a user (typically a seller)
 * @param {Id<"users">} reviewedUserId - The user being reviewed
 * @param {number} overallRating - 1-5
 * @param {number} shippingRating - 1-5 (optional)
 * @param {number} packagingRating - 1-5 (optional)
 * @param {number} accuracyRating - 1-5 (optional)
 * @param {string} reviewText - The review text
 */
export const createUserReview = mutation({
  args: {
    reviewedUserId: v.id("users"),
    overallRating: v.number(),
    shippingRating: v.optional(v.number()),
    packagingRating: v.optional(v.number()),
    accuracyRating: v.optional(v.number()),
    reviewText: v.string(),
  },
  handler: async (ctx, args) => {
    const reviewer = await getCurrentUser(ctx);
    if (!reviewer) throw new Error("Not authenticated");
    if (reviewer._id === args.reviewedUserId)
      throw new Error("Cannot review yourself");
    const valid = (n?: number) => n === undefined || (n >= 1 && n <= 5);
    if (
      !valid(args.overallRating) ||
      !valid(args.shippingRating) ||
      !valid(args.packagingRating) ||
      !valid(args.accuracyRating)
    ) {
      throw new Error("Ratings must be between 1 and 5");
    }
    const existing = await ctx.db
      .query("userReviews")
      .filter((q) =>
        q.and(
          q.eq(q.field("reviewedUserId"), args.reviewedUserId),
          q.eq(q.field("reviewerUserId"), reviewer._id),
        ),
      )
      .first();
    if (existing) throw new Error("You have already reviewed this user");
    await ctx.db.insert("userReviews", {
      reviewedUserId: args.reviewedUserId,
      reviewerUserId: reviewer._id,
      overallRating: args.overallRating,
      shippingRating: args.shippingRating,
      packagingRating: args.packagingRating,
      accuracyRating: args.accuracyRating,
      reviewText: args.reviewText,
      isHidden: false,
    });
    return { success: true };
  },
});

/**
 * @name getUserReviews
 * @description Fetch reviews for a user (with truncation/pagination)
 * @param {Id<"users">} reviewedUserId - The user being reviewed
 * @param {number} limit - Max number of reviews to return
 * @param {number} before - Only reviews created before this timestamp (for pagination)
 */
export const getUserReviews = query({
  args: {
    reviewedUserId: v.id("users"),
    limit: v.optional(v.number()),
    before: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let q = ctx.db
      .query("userReviews")
      .withIndex("by_reviewed_user", (q) =>
        q.eq("reviewedUserId", args.reviewedUserId),
      )
      .filter((q) => q.neq(q.field("isHidden"), true));
    if (args.before !== undefined) {
      q = q.filter((q2) => q2.lt(q2.field("_creationTime"), args.before!));
    }
    let reviews = await q.order("desc").take(args.limit || 10);
    reviews = await Promise.all(
      reviews.map(async (review) => {
        const reviewer = await ctx.db.get(review.reviewerUserId);
        return {
          ...review,
          reviewer: reviewer
            ? {
                _id: reviewer._id,
                name: reviewer.name,
                username: reviewer.username,
                avatarUrl: reviewer.image
                  ? await ctx.storage.getUrl(reviewer.image)
                  : null,
              }
            : null,
        };
      }),
    );
    return reviews;
  },
});

/**
 * @name replyToUserReview
 * @description Seller replies to a review
 * @param {Id<"userReviews">} reviewId - The review to reply to
 * @param {string} replyText - The reply text
 */
export const replyToUserReview = mutation({
  args: {
    reviewId: v.id("userReviews"),
    replyText: v.string(),
  },
  handler: async (ctx, args) => {
    const seller = await getCurrentUser(ctx);
    if (!seller) throw new Error("Not authenticated");
    const review = await ctx.db.get(args.reviewId);
    if (!review) throw new Error("Review not found");
    if (review.reviewedUserId !== seller._id)
      throw new Error("Only the reviewed user can reply");
    await ctx.db.patch(args.reviewId, {
      replyText: args.replyText,
      replyAt: Date.now(),
    });
    return { success: true };
  },
});

/**
 * @name reportUserReview
 * @description Report a review (e.g. for harassment or inaccuracy)
 * @param {Id<"userReviews">} reviewId - The review to report
 * @param {string} reason - The reason (e.g. "harassment", "inaccuracy")
 * @param {string} additionalInfo - Additional info (optional)
 */
export const reportUserReview = mutation({
  args: {
    reviewId: v.id("userReviews"),
    reason: v.string(),
    additionalInfo: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const reporter = await getCurrentUser(ctx);
    if (!reporter) throw new Error("Not authenticated");
    const review = await ctx.db.get(args.reviewId);
    if (!review) throw new Error("Review not found");
    if (
      review.reviewedUserId === reporter._id ||
      review.reviewerUserId === reporter._id
    ) {
      throw new Error("You cannot report your own review");
    }
    await ctx.db.insert("reviewReports", {
      reviewId: args.reviewId,
      reporterUserId: reporter._id,
      reason: args.reason,
      additionalInfo: args.additionalInfo,
      status: "pending",
    });
    return { success: true };
  },
});

/**
 * @name appealUserReview
 * @description Seller appeals a review (e.g. for inaccuracy or harassment)
 * @param {Id<"userReviews">} reviewId - The review to appeal
 * @param {string} type - The type of appeal ("inaccuracy", "harassment")
 * @param {string} message - The appeal message
 */
export const appealUserReview = mutation({
  args: {
    reviewId: v.id("userReviews"),
    type: v.string(),
    message: v.string(),
  },
  handler: async (ctx, args) => {
    const seller = await getCurrentUser(ctx);
    if (!seller) throw new Error("Not authenticated");
    const review = await ctx.db.get(args.reviewId);
    if (!review) throw new Error("Review not found");
    if (review.reviewedUserId !== seller._id)
      throw new Error("Only the reviewed user can appeal");
    const existing = await ctx.db
      .query("reviewAppeals")
      .filter((q) =>
        q.and(
          q.eq(q.field("reviewId"), args.reviewId),
          q.eq(q.field("sellerUserId"), seller._id),
        ),
      )
      .first();
    if (existing) throw new Error("You have already appealed this review");
    await ctx.db.insert("reviewAppeals", {
      reviewId: args.reviewId,
      sellerUserId: seller._id,
      type: args.type,
      message: args.message,
      status: "pending",
    });
    return { success: true };
  },
});

export const setStripeAccountId = internalMutation({
  args: { userId: v.id("users"), stripeAccountId: v.string() },
  handler: async (ctx, { userId, stripeAccountId }) => {
    await ctx.db.patch(userId, { stripeAccountId });
  },
});

/**
 * @name getUserByUsername
 * @description Get a user by their username
 */
export const getUserByUsername = query({
  args: {
    username: v.string(),
  },
  handler: async (ctx, args) => {
    const { username } = args;
    
    const users = await ctx.db
      .query("users")
      .withSearchIndex("search_users", (q) => q.search("username", username).eq("username", username))
      .collect();
    
    if (users.length === 0) {
      return null;
    }
    
    const user = users[0]!;
    
    let avatarUrl = null;
    if (user.image) {
      if (typeof user.image === 'string' && (user.image.startsWith('http://') || user.image.startsWith('https://'))) {
        avatarUrl = user.image;
      } else {
        try {
          avatarUrl = await ctx.storage.getUrl(user.image);
        } catch (error) {
          console.error(`Error getting avatar URL for user ${user._id}:`, error);
          avatarUrl = null;
        }
      }
    }

    let coverImageUrl = null;
    if (user.coverImage) {
      if (typeof user.coverImage === 'string' && (user.coverImage.startsWith('http://') || user.coverImage.startsWith('https://'))) {
        coverImageUrl = user.coverImage;
      } else {
        try {
          coverImageUrl = await ctx.storage.getUrl(user.coverImage);
        } catch (error) {
          console.error(`Error getting cover image URL for user ${user._id}:`, error);
          coverImageUrl = null;
        }
      }
    }
    
    return {
      _id: user._id,
      _creationTime: user._creationTime,
      name: user.name,
      username: user.username,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      image: user.image,
      coverImage: user.coverImage,
      avatarUrl,
      coverImageUrl,
      color: user.color,
      theme: user.theme,
      lastSeen: user.lastSeen,
      status: user.status,
      role: user.role,
      bio: user.bio,
      sellerProfile: user.sellerProfile,
      preferences: user.preferences
    };
  },
});

/**
 * @name getUserFollowers
 * @description Get users who follow a specific user, with pagination
 */
export const getUserFollowers = query({
  args: {
    userId: v.id("users"),
    paginationOpts: v.optional(paginationOptsValidator),
  },
  handler: async (ctx, args) => {
    const { userId, paginationOpts } = args;
    const currentUser = await getCurrentUser(ctx);

    const followsQuery = ctx.db
      .query("follows")
      .withIndex("by_following", (q) => q.eq("followingId", userId))
      .order("desc");

    const defaultPaginationOpts = { numItems: 20, cursor: null };
    const paginatedFollows = await followsQuery.paginate(
      paginationOpts || defaultPaginationOpts,
    );

    if (paginatedFollows.page.length === 0) {
      return {
        page: [],
        isDone: paginatedFollows.isDone,
        continueCursor: paginatedFollows.continueCursor,
      };
    }

    const batchSize = 10;
    const followerUsers = [];

    for (let i = 0; i < paginatedFollows.page.length; i += batchSize) {
      const batchOfFollows = paginatedFollows.page.slice(i, i + batchSize);
      const batchFollowerDetails = await Promise.all(
        batchOfFollows.map(async (follow) => {
          const user = await ctx.db.get(follow.followerId);
          if (!user) return null;

          let avatarUrl = null;
          if (user.image) {
            if (typeof user.image === 'string' && (user.image.startsWith('http://') || user.image.startsWith('https://'))) {
              avatarUrl = user.image;
            } else {
              try {
                avatarUrl = await ctx.storage.getUrl(user.image as Id<"_storage">);
              } catch (error) {
                console.error(`Error getting avatar URL for user ${user._id}:`, error);
                avatarUrl = null;
              }
            }
          }

          let isFollowing = false;
          if (currentUser && currentUser._id !== user._id) {
            const followRelation = await ctx.db
              .query("follows")
              .withIndex("unique_follow", q => q.eq("followerId", currentUser._id).eq("followingId", user._id))
              .first();
            isFollowing = !!followRelation;
          } else if (currentUser && currentUser._id === user._id) {
            isFollowing = false;
          }

          return {
            _id: user._id,
            _creationTime: user._creationTime,
            username: user.username || "",
            name: user.name || "",
            image: user.image,
            avatarUrl,
            isFollowing,
            role: user.role,
          };
        }),
      );
      followerUsers.push(...batchFollowerDetails.filter(Boolean));
    }

    return {
      page: followerUsers,
      isDone: paginatedFollows.isDone,
      continueCursor: paginatedFollows.continueCursor,
    };
  },
});

/**
 * @name getFollowCounts
 * @description Get follower and following counts for a user using the aggregation
 */
export const getFollowCounts = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    try {
      const followers = await ctx.db
        .query("follows")
        .withIndex("by_following", (q) => q.eq("followingId", args.userId))
        .collect();

      const following = await ctx.db
        .query("follows")
        .withIndex("by_follower", (q) => q.eq("followerId", args.userId))
        .collect();

      return {
        followers: followers.length,
        following: following.length,
      };
    } catch (error) {
      console.error("Error fetching follow counts:", error);
      return {
        followers: 0,
        following: 0,
      };
    }
  },
});

/**
 * @name updateCurrentUserPreferences
 * @description Update preferences for the current user without requiring an explicit ID parameter
 */
export const updateCurrentUserPreferences = mutation({
  args: {
    preferences: v.object({
      categories: v.optional(v.array(v.string())),
      subcategories: v.optional(v.array(v.string())),
      notifications: v.optional(v.boolean()),
      emailUpdates: v.optional(v.boolean()),
      darkMode: v.optional(v.boolean()),
      shippingAddresses: v.optional(
        v.array(
          v.object({
            street: v.string(),
            address2: v.optional(v.string()),
            fullName: v.optional(v.string()),
            city: v.string(),
            state: v.string(),
            country: v.string(),
            zipCode: v.string(),
            isDefault: v.optional(v.boolean()),
            isReturn: v.optional(v.boolean()),
          }),
        ),
      ),
    }),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const currentPreferences = user.preferences || {};
    const updatedPreferences = {
      ...currentPreferences,
      ...args.preferences,
    };

    await ctx.db.patch(user._id, {
      preferences: updatedPreferences
    });

    return { success: true };
  },
});

/**
 * @name resetOnboarding
 * @description Reset a user's onboarding status for testing purposes
 */
export const resetOnboarding = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    await ctx.db.patch(user._id, {
      finishedSignUp: false,
      preferences: {
        ...user.preferences,
        categories: [],
        subcategories: []
      }
    });

    return { success: true };
  },
});

/**
 * @name toggleCategoryFollow
 * @description Toggle following a category in user preferences
 * @param {string} categoryId - The ID of the category to toggle follow status
 */
export const toggleCategoryFollow = mutation({
  args: {
    categoryId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const preferences = user.preferences || {};
    const userCategories = preferences.categories || [];
    
    const isFollowed = userCategories.includes(args.categoryId);
    
    let updatedCategories;
    if (isFollowed) {
      updatedCategories = userCategories.filter(id => id !== args.categoryId);
    } else {
      updatedCategories = [...userCategories, args.categoryId];
    }
    
    await ctx.db.patch(user._id, {
      preferences: {
        ...preferences,
        categories: updatedCategories,
      }
    });

    return {
      success: true,
      isFollowed: !isFollowed
    };
  },
});

/**
 * @name toggleSubcategoryFollow
 * @description Toggle following a subcategory in user preferences
 * @param {string} subcategoryId - The ID of the subcategory to toggle follow status
 */
export const toggleSubcategoryFollow = mutation({
  args: {
    subcategoryId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const preferences = user.preferences || {};
    const userSubcategories = preferences.subcategories || [];
    
    const isFollowed = userSubcategories.includes(args.subcategoryId);
    
    let updatedSubcategories;
    if (isFollowed) {
      updatedSubcategories = userSubcategories.filter(id => id !== args.subcategoryId);
    } else {
      updatedSubcategories = [...userSubcategories, args.subcategoryId];
    }
    
    await ctx.db.patch(user._id, {
      preferences: {
        ...preferences,
        subcategories: updatedSubcategories,
      }
    });

    return {
      success: true,
      isFollowed: !isFollowed
    };
  },
});

/**
 * @name areMutualFollowers
 * @description Check if the current user and target user follow each other (are "friends")
 */
export const areMutualFollowers = query({
  args: { targetUserId: v.id("users") },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) return false;

    const userFollowsTarget = await ctx.db
      .query("follows")
      .filter((q) =>
        q.and(
          q.eq(q.field("followerId"), user._id),
          q.eq(q.field("followingId"), args.targetUserId),
        ),
      )
      .first();

    const targetFollowsUser = await ctx.db
      .query("follows")
      .filter((q) =>
        q.and(
          q.eq(q.field("followerId"), args.targetUserId),
          q.eq(q.field("followingId"), user._id),
        ),
      )
      .first();

    return !!userFollowsTarget && !!targetFollowsUser;
  },
});

export const ADMIN_getUsers = query({
  args: {
    searchQuery: v.optional(v.string()),
    searchField: v.optional(v.string()),
    paginationOpts: v.optional(paginationOptsValidator),
    filters: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    let usersQuery: any = ctx.db.query("users");
    let usedSearchIndex = false;

    let indexName: string | undefined;
    let searchField: string | undefined;
    let searchValue: string | undefined;

    if (args.searchQuery && args.searchQuery.trim() !== "") {
      if (args.searchField === "name") {
        indexName = "search_name";
        searchField = "name";
      } else if (args.searchField === "email") {
        indexName = "search_email";
        searchField = "email";
      } else if (args.searchField === "username") {
        indexName = "search_users";
        searchField = "username";
      } else if (args.searchField === "_id") {
        usersQuery = usersQuery.filter((q: any) => 
          q.eq(q.field("_id"), args.searchQuery?.trim())
        );
        usedSearchIndex = false;
      } else {
        indexName = "search_name";
        searchField = "name";
      }

      if (indexName && searchField && args.searchField !== "_id") {
        searchValue = args.searchQuery.trim().toLowerCase();
        usedSearchIndex = true;
      }
    } else if (args.filters && Array.isArray(args.filters)) {
      const searchFilter = args.filters.find(
        (filter: any) => filter.operator === "iLike" && ["name", "email", "username"].includes(filter.id)
      );
      if (searchFilter) {
        indexName = userSearchIndexMap[searchFilter.id as keyof typeof userSearchIndexMap];
        searchField = searchFilter.id;
        searchValue = searchFilter.value;
        usedSearchIndex = true;
      }
    }

    if (usedSearchIndex && indexName && searchField && searchValue && isUserSearchIndexValue(indexName) && isUserSearchField(searchField)) {
      usersQuery = usersQuery.withSearchIndex(indexName, (q: any) =>
        q.search(searchField, searchValue)
      );
    }

    // Remove interestedSellerStatus from DB-side filters
    if (args.filters && Array.isArray(args.filters)) {
      for (const filter of args.filters) {
        if (
          filter.value !== undefined &&
          filter.value !== "" &&
          !(usedSearchIndex && filter.operator === "iLike" && ["name", "email", "username", "_id"].includes(filter.id)) &&
          filter.id !== "interestedSellerStatus"
        ) {
          if (filter.operator === "eq") {
            usersQuery = usersQuery.filter((q: any) =>
              q.eq(q.field(filter.id), filter.value)
            );
          }
          //TODO: Add more operators as needed
        }
      }
    }

    if (!usedSearchIndex) {
      usersQuery = usersQuery.order("desc");
    }

    try {
      const paginationOpts = args.paginationOpts || { numItems: 25, cursor: null };
      const interestedSellerStatusFilter = args.filters?.find(
        (filter: any) => filter.id === "interestedSellerStatus" && filter.operator === "eq"
      );
      const interestedSellerStatusValue = interestedSellerStatusFilter?.value;

      const { page: users, continueCursor, isDone } = await usersQuery.paginate(paginationOpts);

      if (users.length === 0) {
        if (paginationOpts.cursor === null) {
          let fallbackQuery: any;
          if (usedSearchIndex && indexName && searchField && searchValue && isUserSearchIndexValue(indexName) && isUserSearchField(searchField)) {
            fallbackQuery = ctx.db.query("users").withSearchIndex(indexName, (q: any) =>
              q.search(searchField, searchValue)
            );
          } else {
            fallbackQuery = ctx.db.query("users");
          }

          if (args.filters && Array.isArray(args.filters)) {
            for (const filter of args.filters) {
              if (
                filter.value !== undefined &&
                filter.value !== "" &&
                !(usedSearchIndex && filter.operator === "iLike" && ["name", "email", "username"].includes(filter.id)) &&
                filter.id !== "interestedSellerStatus"
              ) {
                if (filter.operator === "eq") {
                  fallbackQuery = fallbackQuery.filter((q: any) =>
                    q.eq(q.field(filter.id), filter.value)
                  );
                }
              }
            }
          }
          const allUsers = await fallbackQuery.collect();
          const paginatedUsers = allUsers.slice(0, paginationOpts.numItems || 25);
          if (paginatedUsers.length > 0) {
            let fallbackUsersWithCounts = await Promise.all(
              paginatedUsers.map(async (user: Doc<"users">) => {
                const followCounts = await ctx.runQuery(api.users.getFollowCounts, { userId: user._id });
                let interestedSellerStatus: string | undefined = undefined;
                if (user.role === "user") {
                  const interestedSeller = await ctx.runQuery(api.interestedSellers.getSellerInterestByUser, { userId: user._id });
                  if (interestedSeller) {
                    interestedSellerStatus = interestedSeller.status;
                  }
                }
                return {
                  _id: user._id,
                  _creationTime: user._creationTime,
                  username: user.username,
                  name: user.name,
                  role: user.role,
                  email: user.email,
                  followersCount: followCounts.followers,
                  interestedSellerStatus,
                };
              })
            );

            if (interestedSellerStatusValue) {
              fallbackUsersWithCounts = fallbackUsersWithCounts.filter(
                user => user.role === "user" && user.interestedSellerStatus === interestedSellerStatusValue
              );
            }
            const hasMore = allUsers.length > paginatedUsers.length;
            const nextCursor = hasMore ? String(paginatedUsers.length) : null;
            return {
              users: fallbackUsersWithCounts,
              continueCursor: nextCursor,
              isDone: !hasMore,
            };
          }
        } else {
          return {
            users: [],
            continueCursor: null,
            isDone: true,
          };
        }
      }

      let usersWithSelectedFields = await Promise.all(
        users.map(async (user: Doc<"users">) => {
          try {
            const followCounts = await ctx.runQuery(api.users.getFollowCounts, { userId: user._id });
            let interestedSellerStatus: string | undefined = undefined;
            if (user.role === "user") {
              const interestedSeller = await ctx.runQuery(api.interestedSellers.getSellerInterestByUser, { userId: user._id });
              if (interestedSeller) {
                interestedSellerStatus = interestedSeller.status;
              }
            }
            return {
              _id: user._id,
              _creationTime: user._creationTime,
              username: user.username,
              name: user.name,
              role: user.role,
              email: user.email,
              followersCount: followCounts.followers,
              interestedSellerStatus,
            };
          } catch (error) {
            console.error(`Error getting follow counts or interestedSeller for user ${user._id}:`, error);
            return {
              _id: user._id,
              _creationTime: user._creationTime,
              username: user.username,
              name: user.name,
              role: user.role,
              email: user.email,
              followersCount: 0,
              interestedSellerStatus: undefined,
            };
          }
        })
      );

      if (interestedSellerStatusValue) {
        usersWithSelectedFields = usersWithSelectedFields.filter(
          user => user.role === "user" && user.interestedSellerStatus === interestedSellerStatusValue
        );
      }

      return {
        users: usersWithSelectedFields,
        continueCursor,
        isDone,
      };
    } catch (error) {
      console.error("Error in ADMIN_getUsers:", error);
      
      return {
        users: [],
        continueCursor: null,
        isDone: true,
      };
    }
  },
});

export const ADMIN_getUsersCount = query({
  args: {
    searchQuery: v.optional(v.string()),
    searchField: v.optional(v.string()),
    filters: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    let usersQuery: any = ctx.db.query("users");
    let usedSearchIndex = false;


    let indexName: string | undefined;
    let searchField: string | undefined;
    let searchValue: string | undefined;

    if (args.searchQuery && args.searchQuery.trim() !== "") {
      if (args.searchField === "name") {
        indexName = "search_name";
        searchField = "name";
      } else if (args.searchField === "email") {
        indexName = "search_email";
        searchField = "email";
      } else if (args.searchField === "username") {
        indexName = "search_users";
        searchField = "username";
      } else if (args.searchField === "_id") {
        usersQuery = usersQuery.filter((q: any) => 
          q.eq(q.field("_id"), args.searchQuery?.trim())
        );
        usedSearchIndex = false;
      } else {
        indexName = "search_name";
        searchField = "name";
      }

      if (indexName && searchField && args.searchField !== "_id") {
        searchValue = args.searchQuery.trim().toLowerCase();
        usedSearchIndex = true;
      }
    } else if (args.filters && Array.isArray(args.filters)) {
      const searchFilter = args.filters.find(
        (filter: any) => filter.operator === "iLike" && ["name", "email", "username"].includes(filter.id)
      );
      if (searchFilter) {
        indexName = userSearchIndexMap[searchFilter.id as keyof typeof userSearchIndexMap];
        searchField = searchFilter.id;
        searchValue = searchFilter.value;
        usedSearchIndex = true;
      }
    }

    if (
      usedSearchIndex &&
      indexName &&
      searchField &&
      searchValue &&
      isUserSearchIndexValue(indexName) &&
      isUserSearchField(searchField)
    ) {
      usersQuery = usersQuery.withSearchIndex(indexName, (q: any) =>
        q.search(searchField, searchValue)
      );
    }

    if (args.filters && Array.isArray(args.filters)) {
      for (const filter of args.filters) {
        if (
          filter.value !== undefined &&
          filter.value !== "" &&
          !(usedSearchIndex && filter.operator === "iLike" && ["name", "email", "username"].includes(filter.id))
        ) {
          if (filter.operator === "eq") {
            usersQuery = usersQuery.filter((q: any) =>
              q.eq(q.field(filter.id), filter.value)
            );
          }
        }
      }
    }

    const users = await usersQuery.collect();
    return { count: users.length };
  },
});

/**
 * @name isUserFollowing
 * @description Check if a specific user is following another user
 */
export const isUserFollowing = query({
  args: { userId: v.id("users"), targetUserId: v.id("users") },
  handler: async (ctx, args) => {
    const follow = await ctx.db
      .query("follows")
      .withIndex("unique_follow", (q) =>
        q.eq("followerId", args.userId)
         .eq("followingId", args.targetUserId)
      )
      .first();

    return !!follow;
  },
});

/**
 * @name ADMIN_getUserById
 * @description Get a user by their ID
 */
export const ADMIN_getUserById = query({
  args: {
    userId: v.id("users"),
  },
  handler: async (
    ctx,
    args
  ): Promise<
    (Doc<"users"> & {
      image?: string | null;
      coverImage?: string | null;
      interestedSeller: Doc<"interestedSellers"> | null;
    }) | null
  > => {
    const { userId } = args;
    
    const userById = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("_id"), userId))
      .first();
    
    if (!userById) {
      return null;
    }
    
    const user = userById;
    
    let avatarUrl = null;
    if (user.image) {
      if (typeof user.image === 'string' && (user.image.startsWith('http://') || user.image.startsWith('https://'))) {
        avatarUrl = user.image;
      } else {
        try {
          avatarUrl = await ctx.storage.getUrl(user.image);
        } catch (error) {
          console.error(`Error getting avatar URL for user ${user._id}:`, error);
          avatarUrl = null;
        }
      }
    }

    let coverImageUrl = null;
    if (user.coverImage) {
      if (typeof user.coverImage === 'string' && (user.coverImage.startsWith('http://') || user.coverImage.startsWith('https://'))) {
        coverImageUrl = user.coverImage;
      } else {
        try {
          coverImageUrl = await ctx.storage.getUrl(user.coverImage);
        } catch (error) {
          console.error(`Error getting cover image URL for user ${user._id}:`, error);
          coverImageUrl = null;
        }
      }
    }
    
    const interestedSeller = await ctx.runQuery(api.interestedSellers.getSellerInterestByUser, { userId: user._id });
    
    return {
      ...user,
      image: avatarUrl ?? undefined,
      coverImage: coverImageUrl ?? undefined,
      interestedSeller
    };
  },
});

/**
 * @name getUserActivities
 * @description Get activities for a given user id, sorted by _creationTime descending
 */
export const getUserActivities = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const activities = await ctx.db
      .query("activities")
      .withIndex("by_object", (q) => q.eq("metadata.objectId", args.userId))
      .order("desc")
      .collect();
    return activities;
  },
});

/**
 * @name getUsersByIds
 * @description Get multiple users by their IDs
 */
export const getUsersByIds = query({
  args: { userIds: v.array(v.id("users")) },
  handler: async (ctx, { userIds }) => {
    return await Promise.all(userIds.map(id => ctx.db.get(id)));
  }
});

export const createUserIfNotExists = mutation(async (ctx, args) => {
  const user = await getCurrentUser(ctx);
  const userId = user?._id as Id<"users">;

  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: "2025-02-24.acacia" });
  const customer = await stripe.customers.create({
    metadata: { userId: userId },
  });

  await ctx.db.patch(userId, { stripeCustomerId: customer.id });

  return { userId: userId, stripeCustomerId: customer.id };
});

export const INTERNAL_updateUserStripeCustomerId = internalMutation({
  args: { userId: v.id("users"), stripeCustomerId: v.string() },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, { stripeCustomerId: args.stripeCustomerId });
  },
});

/**
 * @name savePaymentMethod
 * @description Save a payment method to the user's profile
 */
export const savePaymentMethod = mutation({
  args: {
    paymentMethodId: v.string(),
    last4: v.string(),
    brand: v.string(),
    expMonth: v.number(),
    expYear: v.number(),
    setAsDefault: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const newPaymentMethod = {
      id: args.paymentMethodId,
      last4: args.last4,
      brand: args.brand,
      expMonth: args.expMonth,
      expYear: args.expYear,
      isDefault: args.setAsDefault ?? false,
    };

    const existingMethods = user.paymentMethods || [];

    if (existingMethods.length === 0 || args.setAsDefault) {
      const updatedMethods = existingMethods.map(method => ({
        ...method,
        isDefault: false,
      }));
      updatedMethods.push({ 
        ...newPaymentMethod, 
        brand: newPaymentMethod.brand as "amex" | "diners" | "discover" | "jcb" | "mastercard" | "unionpay" | "visa" | "unknown",
        isDefault: true 
      });
      await ctx.db.patch(user._id, { paymentMethods: updatedMethods });
    } else {
      await ctx.db.patch(user._id, {
        paymentMethods: [...existingMethods, {
          ...newPaymentMethod,
          brand: newPaymentMethod.brand as "amex" | "diners" | "discover" | "jcb" | "mastercard" | "unionpay" | "visa" | "unknown"
        }],
      });
    }

    return { success: true };
  },
});

/**
 * @name setDefaultPaymentMethod
 * @description Set a payment method as default in the user's database record only
 */
export const setDefaultPaymentMethod = mutation({
  args: {
    paymentMethodId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const existingMethods = user.paymentMethods || [];
    const updatedMethods = existingMethods.map(method => ({
      ...method,
      isDefault: method.id === args.paymentMethodId,
    }));

    await ctx.db.patch(user._id, { paymentMethods: updatedMethods });
    return { success: true };
  },
});

/**
 * @name removePaymentMethod
 * @description Remove a payment method from the user's profile and detach it from Stripe
 */
export const removePaymentMethod = action({
  args: {
    paymentMethodId: v.string(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.runQuery(internal.users.INTERNAL_getUser, {});
    if (!user) throw new Error("Not authenticated");

    const existingMethods = user.paymentMethods || [];
    const methodToRemove = existingMethods.find(m => m.id === args.paymentMethodId);
    
    if (!methodToRemove) {
      throw new Error("Payment method not found");
    }

    try {
      await ctx.runAction(api.integration.stripe.detachPaymentMethod, {
        paymentMethodId: args.paymentMethodId
      });

      const updatedMethods = existingMethods
        .filter(m => m.id !== args.paymentMethodId)
        .map(method => ({
          id: method.id,
          last4: method.last4,
          brand: method.brand as "amex" | "diners" | "discover" | "jcb" | "mastercard" | "unionpay" | "visa" | "unknown",
          isDefault: method.isDefault,
          expMonth: method.expMonth,
          expYear: method.expYear
        }));
          
      if (methodToRemove.isDefault && updatedMethods.length > 0) {
        const firstMethod = updatedMethods[0];
        if (firstMethod) {
          firstMethod.isDefault = true;
        }
      }

      await ctx.runMutation(internal.users.INTERNAL_updateUserPaymentMethods, {
        userId: user._id,
        paymentMethods: updatedMethods
      });
      return { success: true };
    } catch (error) {
      console.error("Error removing payment method:", error);
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  },
});

export const INTERNAL_updateUserPaymentMethods = internalMutation({
  args: { 
    userId: v.id("users"), 
    paymentMethods: v.array(v.object({
      id: v.string(),
      last4: v.string(),
      brand: v.union(
        v.literal("amex"),
        v.literal("diners"),
        v.literal("discover"),
        v.literal("jcb"),
        v.literal("mastercard"),
        v.literal("unionpay"),
        v.literal("visa"),
        v.literal("unknown")
      ),
      isDefault: v.boolean(),
      expMonth: v.number(),
      expYear: v.number(),
    }))
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.userId, { paymentMethods: args.paymentMethods });
  },
});

/**
 * @name seedTestUsers
 * @description Insert 150 test users for debugging pagination and infinite scroll
 */
export const seedTestUsers = mutation({
  args: {},
  handler: async (ctx) => {
    const baseEmail = "testuser";
    const domain = "@example.com";
    const usersToInsert = 150;
    const insertedIds = [];
    for (let i = 1; i <= usersToInsert; i++) {
      const email = `${baseEmail}${i}${domain}`;
      const name = `Test User ${i}`;
      const username = `testuser${i}`;
      const userId = await ctx.db.insert("users", {
        email,
        name,
        username,
        role: "user",
      });
      insertedIds.push(userId);
    }
    return { inserted: insertedIds.length };
  },
});

/**
 * @name ADMIN_updateSellerProfile
 * @description Admin: Update a user's seller profile
 */
export const ADMIN_updateSellerProfile = mutation({
  args: {
    userId: v.id("users"),
    sellerProfile: v.object({
      bio: v.optional(v.string()),
      storeName: v.optional(v.string()),
      storeDescription: v.optional(v.string()),
      verified: v.optional(v.boolean()),
      rating: v.optional(v.number()),
      totalSales: v.optional(v.number()),
      joinedAt: v.optional(v.number()),
      percentage: v.optional(v.number()),
    }),
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);

    const user = await ctx.db.get(args.userId);
    if (!user) throw new Error("User not found");

    const oldProfile = user.sellerProfile || {};
    const newProfile = { ...oldProfile, ...args.sellerProfile };
    await ctx.db.patch(args.userId, { sellerProfile: newProfile });

    await ctx.db.insert("activities", {
      type: "admin_updated_seller_profile",
      description: `Admin updated seller profile fields: ${Object.keys(args.sellerProfile).join(", ")}`,
      createdBy: currentUser?._id as Id<"users">,
      metadata: { objectId: args.userId },
    });
    return { success: true };
  },
});

export const getUserProducts = query({
  args: {
    userId: v.id("users"),
    status: v.optional(
      v.union(
        v.literal("active"),
        v.literal("sold_out"),
        v.literal("archived"),
      ),
    ),
  },
  handler: async (ctx, args) => {
    const currentUser = await getCurrentUser(ctx);
    const user = await ctx.db.get(args.userId);
    if (!user) return null;

    if (currentUser) {
      const isBlocked = await ctx.db
        .query("blocks")
        .filter((q) =>
          q.and(
            q.eq(q.field("blockerId"), args.userId),
            q.eq(q.field("blockedId"), currentUser._id),
          ),
        )
        .first();

      if (isBlocked) return null;
    }

    let productsQuery = ctx.db
      .query("products")
      .filter((q) => q.eq(q.field("sellerId"), args.userId));

    if (args.status) {
      productsQuery = productsQuery.filter((q) =>
        q.eq(q.field("status"), args.status),
      );
    }

    const products = await productsQuery.collect();

    const productsWithUrls = await Promise.all(
      products.map(async (product) => {
        const images = Array.isArray(product.images) ? product.images : [];

        const validStorageIds = images.filter(
          (imageId): imageId is Id<"_storage"> => {
            if (typeof imageId !== "string") return false;
            const uuidRegex =
              /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            return uuidRegex.test(imageId);
          },
        );

        const imageUrls = await Promise.all(
          validStorageIds.map(async (imageId) => {
            try {
              return await ctx.storage.getUrl(imageId);
            } catch (error) {
              console.error("Error getting image URL:", error);
              return null;
            }
          }),
        );

        const validImageUrls = imageUrls.filter(
          (url): url is string => url !== null,
        );

        return {
          ...product,
          imageUrls: validImageUrls.length > 0 ? validImageUrls : [],
        };
      }),
    );

    return productsWithUrls;
  },
});

export const aggregatedUsers = query({
  args: {},
  handler: async (ctx) => {
    const users = await aggregateUsers.count(ctx);
    return users;
  }
});