import { mutation, query, action } from "./_generated/server";
import { v } from "convex/values";
import { getCurrentUser } from "./helpers/utils";
import { Id } from "./_generated/dataModel";
import { CURRENCY_VALIDATOR } from "./lib/validators";
import Stripe from "stripe";
import { api, internal } from "./_generated/api";

/**
 * @name createOrder
 * @description Create a new order during a stream
 */
export const createOrder = mutation({
  args: {
    streamId: v.id("streams"),
    productId: v.id("products"),
    quantity: v.number(),
    shippingAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      country: v.string(),
      zipCode: v.string(),
    }),
    paymentIntentId: v.optional(v.string()),
    totalAmount: v.number(),
    currency: v.string(),
  },
  handler: async (ctx, args) => {
    const buyer = await getCurrentUser(ctx);
    if (!buyer) throw new Error("Not authenticated");

    const stream = await ctx.db.get(args.streamId);
    if (!stream) throw new Error("Stream not found");
    if (stream.status !== "live") throw new Error("Stream is not live");

    const product = await ctx.db.get(args.productId);
    if (!product) throw new Error("Product not found");
    if (product.status !== "active")
      throw new Error("Product is not available");
    if (!product.inventory || product.inventory < args.quantity)
      throw new Error("Not enough inventory");

    const orderId = await ctx.db.insert("orders", {
      buyerId: buyer._id,
      sellerId: stream.hostId as Id<"users">,
      streamId: args.streamId as Id<"streams">,
      productId: args.productId,
      quantity: args.quantity,
      totalAmount: args.totalAmount,
      currency: args.currency,
      status: "pending",
      shippingAddress: args.shippingAddress,
      updatedAt: Date.now(),
      paymentIntentId: args.paymentIntentId ?? "",
    });

    await ctx.db.patch(args.productId, {
      inventory: product.inventory - args.quantity,
      status: product.inventory - args.quantity === 0 ? "sold_out" : "active",
    });

    return { orderId, paymentIntentId: args.paymentIntentId };
  },
});

/**
 * @name updateOrderStatus
 * @description Update an order's status (seller only)
 */
export const updateOrderStatus = mutation({
  args: {
    orderId: v.id("orders"),
    status: v.union(
      v.literal("confirmed"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled"),
      v.literal("refunded"),
    ),
    trackingNumber: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const order = await ctx.db.get(args.orderId);
    if (!order) throw new Error("Order not found");
    if (order.sellerId !== user._id) throw new Error("Not authorized");

    const updates: any = {
      status: args.status,
      updatedAt: Date.now(),
    };

    if (args.trackingNumber) {
      updates.trackingNumber = args.trackingNumber;
    }

    await ctx.db.patch(args.orderId, updates);

    if (args.status === "cancelled" || args.status === "refunded") {
      const product = await ctx.db.get(order.productId);
      if (product) {
        await ctx.db.patch(order.productId, {
          inventory: (product.inventory || 0) + order.quantity,
          status: "active",
        });
      }
    }
  },
});

/**
 * @name getBuyerOrders
 * @description Get orders for the current user
 */
export const getBuyerOrders = query({
  args: {
    status: v.optional(
      v.union(
        v.literal("pending"),
        v.literal("confirmed"),
        v.literal("shipped"),
        v.literal("delivered"),
        v.literal("cancelled"),
        v.literal("refunded"),
      ),
    ),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    let query = ctx.db
      .query("orders")
      .filter((q) => q.eq(q.field("buyerId"), user._id))
      .order("desc");

    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }

    const orders = await query.collect();

    const enrichedOrders = await Promise.all(
      orders.map(async (order) => {
        const [product, seller] = await Promise.all([
          ctx.db.get(order.productId),
          ctx.db.get(order.sellerId),
        ]);
        return {
          ...order,
          product,
          seller,
        };
      }),
    );

    return enrichedOrders;
  },
});

/**
 * @name getSellerOrders
 * @description Get orders for the seller
 */
export const getSellerOrders = query({
  args: {
    status: v.optional(
      v.union(
        v.literal("pending"),
        v.literal("confirmed"),
        v.literal("shipped"),
        v.literal("delivered"),
        v.literal("cancelled"),
        v.literal("refunded"),
      ),
    ),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");
    if (user.role !== "seller") throw new Error("Not authorized");

    let query = ctx.db
      .query("orders")
      .filter((q) => q.eq(q.field("sellerId"), user._id))
      .order("desc");

    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }

    const orders = await query.collect();

    const enrichedOrders = await Promise.all(
      orders.map(async (order) => {
        const [product, buyer] = await Promise.all([
          ctx.db.get(order.productId),
          ctx.db.get(order.buyerId),
        ]);
        return {
          ...order,
          product,
          buyer,
        };
      }),
    );

    return enrichedOrders;
  },
});

/**
 * @name createDirectOrder
 * @description Create a new order outside of a stream
 */
export const createDirectOrder = mutation({
  args: {
    productId: v.id("products"),
    quantity: v.number(),
    shippingAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      country: v.string(),
      zipCode: v.string(),
    }),
    paymentIntentId: v.string(),
    totalAmount: v.number(),
    currency: v.string(),
  },
  handler: async (ctx, args) => {
    const buyer = await getCurrentUser(ctx);
    if (!buyer) throw new Error("Not authenticated");

    const product = await ctx.db.get(args.productId);
    if (!product) throw new Error("Product not found");
    if (product.status !== "active")
      throw new Error("Product is not available");
    if (!product.inventory || product.inventory < args.quantity)
      throw new Error("Not enough inventory");

    const orderId = await ctx.db.insert("orders", {
      buyerId: buyer._id,
      sellerId: product.sellerId as Id<"users">,
      productId: args.productId,
      quantity: args.quantity,
      totalAmount: args.totalAmount,
      currency: args.currency,
      status: "pending",
      shippingAddress: args.shippingAddress,
      updatedAt: Date.now(),
      paymentIntentId: args.paymentIntentId,
    });

    await ctx.db.patch(args.productId, {
      inventory: product.inventory - args.quantity,
      status: product.inventory - args.quantity === 0 ? "sold_out" : "active",
    });

    return { orderId };
  },
});

export const createOrderWithStripe = action({
  args: {
    streamId: v.optional(v.id("streams")),
    productId: v.id("products"),
    quantity: v.number(),
    shippingAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      country: v.string(),
      zipCode: v.string(),
    }),
    isDirect: v.optional(v.boolean()),
  },
  handler: async (ctx, args): Promise<{ orderId: string; clientSecret: string | null }> => {

    const product = await ctx.runQuery(api.products.getProduct, { productId: args.productId });
    if (!product) throw new Error("Product not found");
    if (product.status !== "active") throw new Error("Product is not available");
    if (!product.inventory || product.inventory < args.quantity) throw new Error("Not enough inventory");

    let sellerId: Id<"users">;
    let streamHostId: Id<"users"> | undefined;
    if (args.streamId && !args.isDirect) {

      const stream = await ctx.runQuery(api.streams.getStream, { streamId: args.streamId });
      if (!stream) throw new Error("Stream not found");
      if (stream.status !== "live") throw new Error("Stream is not live");
      sellerId = stream.hostId as Id<"users">;
      streamHostId = sellerId;
    } else {
      sellerId = product.sellerId as Id<"users">;
    }
    const seller = await ctx.runQuery(internal.users.INTERNAL_getUser);
    if (!seller || !seller.stripeAccountId) throw new Error("Seller is not connected to Stripe");

    // Check seller's Stripe account capabilities
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: "2025-02-24.acacia" });
    const stripeAccount = await stripe.accounts.retrieve(seller.stripeAccountId);
    if (!stripeAccount.capabilities || stripeAccount.capabilities.transfers !== "active") {
      throw new Error("Seller's Stripe account is not ready to receive payouts. Please complete onboarding and enable transfers.");
    }

    const totalAmount = product.price && product.price * args.quantity;
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round((totalAmount ?? 0) * 100),
      currency: product.currency || "usd",
      payment_method_types: ["card"],
      transfer_data: { destination: seller.stripeAccountId },
    });

    let orderResult;
    if (args.streamId && !args.isDirect) {
      orderResult = await ctx.runMutation(api.orders.createOrder, {
        streamId: args.streamId,
        productId: args.productId,
        quantity: args.quantity,
        shippingAddress: args.shippingAddress,
        paymentIntentId: paymentIntent.id,
        totalAmount: totalAmount ?? 0,
        currency: product.currency || "usd",
      });
    } else {
      orderResult = await ctx.runMutation(api.orders.createDirectOrder, {
        productId: args.productId,
        quantity: args.quantity,
        shippingAddress: args.shippingAddress,
        paymentIntentId: paymentIntent.id,
        totalAmount: totalAmount ?? 0,
        currency: product.currency || "usd",
      });
    }
    return { orderId: orderResult.orderId, clientSecret: paymentIntent.client_secret };
  },
});
