import { query } from "./_generated/server";
import { v } from "convex/values";
import { getCurrentUser } from "./helpers/utils";

export const getReports = query({
  args: {
    filter: v.optional(
      v.union(v.literal("all"), v.literal("pending"), v.literal("closed")),
    ),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    if (!user) {
      throw new Error("Unauthorized");
    }

    let reportsQuery = ctx.db
      .query("reports")
      .withIndex("by_reporter", (q) => q.eq("reportedByUserId", user._id))
      .order("desc");

    if (args.filter === "pending") {
      reportsQuery = reportsQuery.filter((q) =>
        q.eq(q.field("status"), "pending"),
      );
    } else if (args.filter === "closed") {
      reportsQuery = reportsQuery.filter((q) =>
        q.eq(q.field("status"), "closed"),
      );
    }

    const reports = await reportsQuery.collect();

    const reportedUsers = await Promise.all(
      reports.map((report) => ctx.db.get(report.reportedUserId)),
    );

    const sortedReports = reports.sort((a, b) => b.updatedAt - a.updatedAt);

    return sortedReports.map((report, index) => ({
      ...report,
      reportedUser: reportedUsers[index],
    }));
  },
});
