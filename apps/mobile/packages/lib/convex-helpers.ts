/**
 * Convex-specific helper functions
 */

const CONVEX_SITE_URL = process.env.EXPO_PUBLIC_CONVEX_SITE_URL;

/**
 * Checks if a string is a valid Convex storage ID format
 * @param id The string to check
 * @returns boolean indicating if the string matches a storage ID pattern
 */
export function isConvexStorageId(id: string | null | undefined): boolean {
  if (!id) return false;
  return (
    typeof id === "string" &&
    (/^[0-9a-fA-F-]{36}$/.test(id) || /^[a-z0-9]{32,}$/.test(id))
  );
}

/**
 * Converts a Convex storage ID to a proper URL for that resource
 * @param storageId The storage ID to convert
 * @returns A URL that can be used to access the file
 */
export function getConvexFileUrl(
  storageId: string | null | undefined,
): string | undefined {
  if (!storageId) return undefined;
  if (isConvexStorageId(storageId)) {
    return `${CONVEX_SITE_URL}/getImage?storageId=${storageId}`;
  }
  return undefined;
}

/**
 * Gets a properly formatted image URL, handling Convex storage IDs and direct URLs
 * @param imageSource The image source (storage ID or direct URL)
 * @param fallbackUrl The URL to use if the image source is invalid
 * @returns A properly formatted image URL
 */
export function getImageUrl(image?: string) {
  if (typeof image !== "string" || !image) return undefined;
  const sanitized = image.trim().replace(/^=+/, "");
  if (!CONVEX_SITE_URL) {
    console.warn("CONVEX_SITE_URL is not defined");
    return undefined;
  }
  if (sanitized.startsWith("http://") || sanitized.startsWith("https://")) {
    return sanitized;
  }
  if (sanitized.includes("/getImage?storageId=")) {
    return sanitized;
  }
  // If it's a storage ID, use the new format
  return `${CONVEX_SITE_URL}/?getImage=storageId=${sanitized}`;
}

/**
 * Creates a fallback avatar URL using UI Avatars service
 * @param username The username for the avatar
 * @param color The background color (with or without #)
 * @returns A URL for a generated avatar with first initial
 */
export function getAvatarFallbackUrl(
  username: string = "User",
  color: string = "2C2C2E",
): string {
  const colorValue = color.replace("#", "");
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(username)}&background=${colorValue}&color=ffffff&size=256`;
}
