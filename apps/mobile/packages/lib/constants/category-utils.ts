import { categories, subcategories } from "./categories"

/**
 * Returns the title for a given category id.
 * @param id - The category id
 * @returns The category title or undefined if not found
 */
export function getCategoryTitleById(id: string): string | undefined {
  return categories.find((cat) => cat.id === id)?.title
}

/**
 * Returns the title for a given subcategory id within a category.
 * @param categoryId - The parent category id
 * @param subcategoryId - The subcategory id
 * @returns The subcategory title or undefined if not found
 */
export function getSubcategoryTitleById(categoryId: string, subcategoryId: string): string | undefined {
  const subs = subcategories[categoryId]
  if (!subs) return undefined
  return subs.find((sub) => sub.id === subcategoryId)?.title
} 