import * as React from "react";

import { cn } from "@workspace/ui/lib/utils";

function Input({
  className,
  type,
  leftIcon,
  rightIcon,
  ...props
}: React.ComponentProps<"input"> & {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}) {
  return (
    <div className="relative flex items-center w-full">
      {leftIcon && (
        <div className="absolute left-3 flex items-center pointer-events-none text-muted-foreground h-4 w-4">
          {leftIcon}
        </div>
      )}
      <input
        type={type}
        data-slot="input"
        className={cn(
          "file:text-foreground placeholder:text-muted-foreground rounded-md selection:bg-primary/20 selection:text-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          "focus-visible:ring-ring/50 focus-visible:ring-[3px] selection:!bg-blue-500/50",
          "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
          leftIcon && "pl-10",
          rightIcon && "pr-10",
          className,
        )}
        {...props}
      />
      {rightIcon && (
        <div className="absolute right-3 flex items-center pointer-events-none text-muted-foreground">
          {rightIcon}
        </div>
      )}
    </div>
  );
}

export { Input };
