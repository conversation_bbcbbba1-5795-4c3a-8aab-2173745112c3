"use client";

import { format } from "date-fns";
import { useState } from "react";
import { cn } from "@workspace/ui/lib/utils";
import { Button, buttonVariants } from "@workspace/ui/components/button";
import { Calendar } from "@workspace/ui/components/calendar";
import { Input } from "@workspace/ui/components/input";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import * as chrono from "chrono-node";
import {
  IconChevronLeft,
  IconChevronRight,
  IconSquareRoundedCheckFilled,
} from "@tabler/icons-react";
import React from "react";

interface DatetimePickerProps {
  value?: Date;
  onChange?: (date: Date | undefined) => void;
  disabled?: boolean;
  onClose?: () => void;
}

const TIMES = Array.from({ length: 24 * 60 }, (_, i) => {
  const hour = Math.floor(i / 60);
  const minute = i % 60;
  return `${hour.toString().padStart(2, "0")}:${minute.toString().padStart(2, "0")}`;
});

const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const defaultSuggestions = [
  "Tomorrow",
  "Tomorrow morning",
  "Tomorrow night",
  "Next Monday",
  "Next Sunday",
];

export function DatetimePicker({
  value,
  onChange,
  disabled,
  onClose,
}: DatetimePickerProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(value || new Date());
  const [inputValue, setInputValue] = useState(
    value ? format(value, "PPP 'at' h:mm a") : "",
  );
  const [currentMonth, setCurrentMonth] = useState<Date>(value || new Date());

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    const parsedDate = chrono.parseDate(newValue);
    if (parsedDate) {
      const newDate = new Date(parsedDate);
      const parsedTime = chrono.parse(newValue)[0]?.start.isCertain("hour");
      if (!parsedTime) {
        newDate.setHours(selectedDate.getHours(), selectedDate.getMinutes());
      }
      setSelectedDate(newDate);
      setCurrentMonth(newDate);
      onChange?.(newDate);
    }
  };

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const parsedDate = chrono.parseDate(inputValue);
    if (parsedDate) {
      const newDate = new Date(parsedDate);
      const parsedTime = chrono.parse(inputValue)[0]?.start.isCertain("hour");
      if (!parsedTime) {
        newDate.setHours(selectedDate.getHours(), selectedDate.getMinutes());
      }
      setSelectedDate(newDate);
      setInputValue(format(newDate, "PPP 'at' h:mm a"));
      onChange?.(newDate);
    }
  };

  const handleDateSelect = (newDate: Date | null) => {
    if (newDate) {
      newDate.setHours(selectedDate.getHours(), selectedDate.getMinutes());
      setSelectedDate(newDate);
      setInputValue(format(newDate, "PPP"));
      onChange?.(newDate);
    }
  };

  const handleTimeSelect = (timeStr: string) => {
    const [hours, minutes] = timeStr.split(":").map(Number);
    const newDate = new Date(selectedDate);
    newDate.setHours(hours, minutes);
    setSelectedDate(newDate);
    setInputValue(format(newDate, "PPP"));
    onChange?.(newDate);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const parsedDate = chrono.parseDate(inputValue);
      if (parsedDate) {
        const newDate = new Date(parsedDate);
        const parsedTime = chrono.parse(inputValue)[0]?.start.isCertain("hour");
        if (!parsedTime) {
          newDate.setHours(selectedDate.getHours(), selectedDate.getMinutes());
        }
        setSelectedDate(newDate);
        setInputValue(format(newDate, "PPP 'at' h:mm a"));
        onChange?.(newDate);
      }
      onClose?.();
    }
  };

  const scrollTimeIntoView = (time: string) => {
    const timeElement = document.getElementById(`time-${time}`);
    if (timeElement) {
      timeElement.scrollIntoView({ block: "center", behavior: "smooth" });
    }
  };

  React.useEffect(() => {
    const currentTime = format(selectedDate, "HH:mm");
    scrollTimeIntoView(currentTime);
  }, [selectedDate]);

  return (
    <div className="flex flex-col space-y-4">
      <div className="space-y-2">
        <Input
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          onKeyDown={handleKeyDown}
          className="w-full"
          placeholder="Enter a date (e.g. Tomorrow morning)"
        />
      </div>

      <div className="flex gap-4">
        <div className="flex-1 space-y-4">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleDateSelect}
            disabled={disabled}
            className="rounded-md border w-full p-3"
            month={currentMonth}
            onMonthChange={setCurrentMonth}
            fromDate={new Date(new Date().getFullYear(), 0, 1)}
            toDate={new Date(new Date().getFullYear() + 10, 11, 31)}
            classNames={{
              months: "w-full",
              month: "w-full space-y-4",
              caption: "flex justify-center pt-1 relative items-center w-full",
              caption_label: "text-sm font-medium",
              nav: "flex items-center gap-1",
              nav_button: cn(
                buttonVariants({ variant: "outline" }),
                "size-7 bg-transparent p-0 opacity-50 hover:opacity-100",
              ),
              nav_button_previous: "absolute left-1",
              nav_button_next: "absolute right-1",
              table: "w-full border-collapse",
              head_row: "flex w-full",
              head_cell:
                "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem] flex-1",
              row: "flex w-full mt-2",
              cell: "rounded-lg text-center text-sm relative p-0 flex-1 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
              day: "cursor-pointer h-9 w-full p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground rounded-md",
              day_selected:
                "bg-blue-500/50 border border-blue-500 text-accent-foreground hover:bg-blue-600/90 focus:bg-blue-500/90 focus:text-primary-foreground rounded-md",
              day_today: "bg-accent text-accent-foreground",
              day_outside: "text-muted-foreground opacity-50",
              day_disabled: "text-muted-foreground opacity-50",
              day_range_middle:
                "aria-selected:bg-accent aria-selected:text-accent-foreground",
              day_hidden: "invisible",
              day_range_start:
                "day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",
              day_range_end:
                "day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",
            }}
            components={{
              IconLeft: () => <IconChevronLeft className={cn("size-4")} />,
              IconRight: () => <IconChevronRight className={cn("size-4")} />,
            }}
          />
        </div>

        <ScrollArea className="h-[305px] w-[120px] border border-input rounded-md p-2">
          <div className="space-y-1">
            {TIMES.map((time) => (
              <div
                key={time}
                id={`time-${time}`}
                onWheel={(e) => e.stopPropagation()}
              >
                <Button
                  type="button"
                  variant="outline"
                  className={cn(
                    "w-full justify-between items-center pr-2",
                    format(selectedDate, "HH:mm") === time &&
                      "bg-primary !bg-blue-500/50 hover:!bg-blue-500/90 text-accent-foreground border !border-blue-400",
                  )}
                  onClick={() => handleTimeSelect(time)}
                >
                  <span>{format(new Date(0, 0, 0, ...time.split(":").map(Number)), "h:mm a")}</span>
                  {format(selectedDate, "HH:mm") === time && (
                    <IconSquareRoundedCheckFilled
                      className="w-4 h-4 text-blue-400"
                      stroke={1.5}
                    />
                  )}
                </Button>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}
