"use client";

import * as React from "react";
import { Button } from "./button";
import { cn } from "../lib/utils";
import type { Icon as TablerIcon } from "@tabler/icons-react";

export interface IconButtonProps
  extends React.ComponentPropsWithoutRef<typeof Button> {
  icon: TablerIcon;
}

export function IconButton({
  icon: Icon,
  className,
  onClick,
  ...props
}: IconButtonProps) {
  return (
    <Button
      variant="ghost"
      size="icon"
      className={cn("size-7 relative", className)}
      onClick={onClick}
      {...props}
    >
      <Icon className="size-5" />
      <span className="sr-only">{props["aria-label"] || "Button"}</span>
    </Button>
  );
}
