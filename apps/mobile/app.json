{"expo": {"name": "Liveciety", "slug": "mobile-liveciety", "version": "1.0.10", "orientation": "portrait", "icon": "./assets/icon.png", "scheme": "liveciety", "userInterfaceStyle": "automatic", "newArchEnabled": true, "jsEngine": "hermes", "ios": {"supportsTablet": true, "bundleIdentifier": "com.liveciety.app", "buildNumber": "1", "infoPlist": {"NSCameraUsageDescription": "We need camera access to let you take and share photos in your chat conversations with other users.", "NSPhotoLibraryUsageDescription": "We need photo library access to let you select and share existing photos in your chat conversations.", "NSPhotoLibraryAddUsageDescription": "We need photo library access to save photos shared in your conversations to your device.", "NSUserTrackingUsageDescription": "We use this data to provide you with a better and more personalized experience, and to help our app grow and improve.", "ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.liveciety.app", "permissions": ["com.google.android.gms.permission.AD_ID"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#18181B"}], "expo-tracking-transparency", "expo-updates", "expo-secure-store", "@livekit/react-native-expo-plugin", "@config-plugins/react-native-webrtc"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "7bcea97d-6ff5-4f1e-9a97-3f3150166491"}}, "owner": "liveciety", "runtimeVersion": {"policy": "appVersion"}, "updates": {"enabled": false}}}