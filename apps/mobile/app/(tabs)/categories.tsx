import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  LayoutChangeEvent,
  ImageBackground,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { categories, subcategories } from "@workspace/lib/constants/categories";
import Header from "../../components/ui/Header";
import CategoryGridSkeleton from "../../components/skeletons/CategoryGridSkeleton";
import SubcategoriesSkeleton from "../../components/skeletons/SubcategoriesSkeleton";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import { getImageUrl } from "@workspace/lib/convex-helpers";

const viewerCounts: Record<string, number> = {
  trading_cards: 3100,
  sports_cards: 4200,
  sports_memorabilia: 450,
  toys_hobbies: 842,
  electronics: 758,
  mens_fashion: 520,
  sneakers_streetwear: 670,
  bags_accessories: 339,
  womens_fashion: 1000,
  beauty: 305,
  jewelry: 280,
  music: 150,
  video_games: 890,
  antiques_vintage: 230,
  home_garden: 180,
  rocks_crystals: 120,
  baby_kids: 95,
  anime_manga: 760,
  sporting_goods: 340,
};

export default function CategoriesScreen() {
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(
    null,
  );
  const [selectedTab, setSelectedTab] = useState<string | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const rowPositions = useRef<Record<number, number>>({});
  const headerHeight = useRef<number>(0);
  const tabsHeight = useRef<number>(0);
  const topPadding = 70;
  const toggleBookmark = useMutation(api.bookmarks.toggleBookmark);
  const [bookmarkRefresh, setBookmarkRefresh] = useState(0);
  const router = useRouter();

  const user = useQuery(api.users.viewer, {});
  const followedCategories = user?.preferences?.categories ?? [];
  const followedSubcategories = user?.preferences?.subcategories ?? [];

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const allStreams = useQuery(
    api.streams.listByCategory,
    selectedCategory && selectedSubcategory === "all"
      ? { category: selectedCategory }
      : "skip",
  );

  const subcategoryStreams = useQuery(
    api.streams.listByCategory,
    selectedCategory && selectedSubcategory && selectedSubcategory !== "all"
      ? { category: selectedCategory, subcategory: selectedSubcategory }
      : "skip",
  );

  const tabStreams = useQuery(
    api.streams.listByCategory,
    selectedCategory && selectedTab
      ? { category: selectedCategory, subcategory: selectedTab }
      : "skip",
  );

  const isFollowed =
    selectedCategory && followedCategories.includes(selectedCategory);
  const isSubcategoryFollowed =
    selectedSubcategory && followedSubcategories.includes(selectedSubcategory);
  const handleFollowToggle = () => {
  };

  const handleCategoryPress = (categoryId: string, rowIndex: number) => {
    if (categoryId === selectedCategory) {
      setSelectedCategory(null);
      setSelectedSubcategory(null);
      setSelectedTab(null);
      return;
    }
    setSelectedCategory(categoryId);
    setSelectedSubcategory(null);
    setSelectedTab(null);
    if (scrollViewRef.current) {
      const rowPosition = rowPositions.current[rowIndex] || 0;
      const targetPosition = Math.max(0, rowPosition);
      scrollViewRef.current.scrollTo({
        y: targetPosition,
        animated: true,
      });
    }
  };

  const handleHeaderLayout = (event: LayoutChangeEvent) => {
    headerHeight.current = event.nativeEvent.layout.height;
  };

  const handleTabsLayout = (event: LayoutChangeEvent) => {
    tabsHeight.current = event.nativeEvent.layout.height;
  };

  const handleRowLayout = (rowIndex: number, event: LayoutChangeEvent) => {
    rowPositions.current[rowIndex] = event.nativeEvent.layout.y;
  };

  const renderViewerCount = (count: number) => (
    <View style={styles.viewersContainer}>
      <View style={styles.liveIndicator} />
      <Text style={styles.viewersText}>{count} Viewers</Text>
    </View>
  );

  const StreamCard = ({
    stream,
    isBookmarked,
    onPress,
  }: {
    stream: any;
    isBookmarked: boolean;
    onPress?: () => void;
  }) => (
    <View
      style={{
        width: "48%",
        aspectRatio: 0.75,
        marginBottom: 16,
        borderRadius: 16,
        overflow: "hidden",
        backgroundColor: "#232325",
        position: "relative",
      }}
    >
      <TouchableOpacity
        style={{ flex: 1 }}
        activeOpacity={0.85}
        onPress={onPress}
      >
        <ImageBackground
          source={{ uri: stream.thumbnailUrl }}
          style={{ flex: 1, justifyContent: "space-between" }}
          imageStyle={{ borderRadius: 16 }}
          resizeMode="cover"
        >
          {/* Top overlays */}
          <View style={{ padding: 10 }}>
            {/* Live/Scheduled indicator */}
            <View
              style={{
                backgroundColor: stream.isLive ? "#ff2d55" : "#1a96d2",
                paddingHorizontal: 8,
                paddingVertical: 4,
                borderRadius: 8,
                alignSelf: "flex-start",
                marginBottom: 8,
              }}
            >
              <Text style={{ color: "#fff", fontSize: 12, fontWeight: "bold" }}>
                {stream.isLive ? "Live" : "Scheduled"}
              </Text>
            </View>

            {/* Bookmark button */}
            <TouchableOpacity
              onPress={async () => {
                await toggleBookmark({ streamId: stream._id });
                setBookmarkRefresh((prev) => prev + 1);
              }}
              style={{
                position: "absolute",
                top: 10,
                right: 10,
                padding: 4,
              }}
            >
              <Ionicons
                name={isBookmarked ? "bookmark" : "bookmark-outline"}
                size={18}
                color="#fff"
              />
            </TouchableOpacity>
          </View>
          {/* Bottom overlays */}
          <LinearGradient
            colors={["transparent", "rgba(0,0,0,0.8)"]}
            style={{
              position: "absolute",
              left: 0,
              right: 0,
              bottom: 0,
              height: 80,
              justifyContent: "flex-end",
              padding: 10,
            }}
          >
            <Text
              style={{ color: "#fff", fontWeight: "bold", fontSize: 16 }}
              numberOfLines={1}
            >
              {stream.title}
            </Text>
            <Text style={{ color: "#1a96d2", fontSize: 13 }} numberOfLines={1}>
              {stream.subcategory}
            </Text>
            {/* Username */}
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginTop: 8,
              }}
            >
              {stream.streamer?.image && (
                <Image
                  source={{ uri: getImageUrl(stream.streamer?.image) }}
                  style={{
                    width: 24,
                    height: 24,
                    borderRadius: 12,
                    borderWidth: 2,
                    borderColor: "#fff",
                    marginRight: 8,
                  }}
                />
              )}
              <Text
                style={{
                  color: "#fff",
                  fontWeight: "bold",
                  fontSize: 15,
                  textShadowColor: "#000",
                  textShadowRadius: 2,
                }}
              >
                {stream.username}
              </Text>
            </View>
          </LinearGradient>
        </ImageBackground>
      </TouchableOpacity>
    </View>
  );

  const renderStreamsGrid = (streams: any[], key?: number) => (
    <View
      key={key}
      style={{
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "space-between",
        padding: 8,
      }}
    >
      {streams.map((stream) => (
        <StreamCard
          key={stream._id}
          stream={stream}
          isBookmarked={!!stream.isBookmarked}
          onPress={() => router.push(`/screens/stream/${stream._id}`)}
        />
      ))}
    </View>
  );

  const renderSubcategories = (selectedId: string) => {
    const categorySubcategories = subcategories[selectedId] || [];
    const selectedCategoryObj = categories.find((c) => c.id === selectedId);

    return (
      <View style={styles.subcategoriesSection}>
        <TouchableOpacity onPress={() => setSelectedSubcategory("all")}>
          <View style={styles.allCategoryItem}>
            <View style={styles.subcategoryIcon}>
              <Image
                source={{ uri: getImageUrl(subcategories[selectedId]?.[0]?.image) }}
                style={styles.subcategoryImage}
                resizeMode="contain"
              />
            </View>
            <View style={styles.subcategoryContent}>
              <Text style={styles.subcategoryTitle}>
                All {selectedCategoryObj?.title}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
        {categorySubcategories.map((subcategory) => (
          <TouchableOpacity
            key={subcategory.id}
            onPress={() => setSelectedSubcategory(subcategory.id)}
          >
            <View style={styles.subcategoryItem}>
              <View style={styles.subcategoryIcon}>
                <Image
                  source={{ uri: getImageUrl(subcategory.image) }}
                  style={styles.subcategoryImage}
                  resizeMode="cover"
                />
              </View>
              <View style={styles.subcategoryContent}>
                <Text style={styles.subcategoryTitle}>{subcategory.title}</Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderAllView = () => {
    const categorySubcategories =
      subcategories[selectedCategory ? selectedCategory : ""] || [];
    return (
      <View style={{ flex: 1 }}>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            padding: 16,
          }}
        >
          <Text style={{ color: "#fff", fontSize: 22, fontWeight: "bold" }}>
            {categories.find((c) => c.id === selectedCategory)?.title}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: isFollowed ? "#232325" : "#fff",
              borderRadius: 20,
              paddingHorizontal: 16,
              paddingVertical: 6,
              borderWidth: 1,
              borderColor: "#fff",
            }}
            onPress={handleFollowToggle}
          >
            <Text
              style={{
                color: isFollowed ? "#fff" : "#232325",
                fontWeight: "bold",
              }}
            >
              {isFollowed ? "Unfollow" : "Follow"}
            </Text>
          </TouchableOpacity>
        </View>
        {/* Horizontal tab bar for filters */}
        {/* <ScrollView horizontal style={{ marginBottom: 8, paddingHorizontal: 8 }} showsHorizontalScrollIndicator={false}>
          {['Recommended', 'Buy It Now', 'Top Sellers'].map(tab => (
            <TouchableOpacity
              key={tab}
              style={{
                backgroundColor: selectedTab === tab ? '#fff' : '#232325',
                borderRadius: 16,
                paddingHorizontal: 16,
                paddingVertical: 8,
                marginRight: 8,
              }}
              onPress={() => setSelectedTab(tab)}
            >
              <Text style={{ color: selectedTab === tab ? '#232325' : '#fff', fontWeight: 'bold' }}>{tab}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView> */}
        {/* Subcategory tabs (below filter tabs) */}
        <View style={{ justifyContent: "center" }}>
          <ScrollView
            horizontal
            style={{ marginBottom: 8, paddingHorizontal: 8 }}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ alignItems: "center", height: 100 }}
          >
            {categorySubcategories.map(
              (subcat: { id: string; title: string; image?: string }) => (
                <TouchableOpacity
                  key={subcat.id}
                  style={{
                    borderRadius: 16,
                    marginRight: 8,
                    flex: 1,
                    justifyContent: "center",
                    alignItems: "center",
                    height: 100,
                    overflow: "hidden",
                  }}
                  onPress={() => setSelectedTab(subcat.id)}
                  activeOpacity={0.85}
                >
                  <ImageBackground
                    source={{ uri: getImageUrl(subcat.image) }}
                    style={{ width: "100%", height: "100%", justifyContent: "flex-end" }}
                    imageStyle={{ borderRadius: 16, opacity: selectedTab === subcat.id ? 1 : 0.7 }}
                    resizeMode="cover"
                  >
                    <LinearGradient
                      colors={["rgba(28,28,30,0.85)", "rgba(28,28,30,0.8)", "transparent"]}
                      start={{ x: 0.5, y: 1 }}
                      end={{ x: 0.5, y: 0 }}
                      style={{
                        width: "100%",
                        paddingHorizontal: 16,
                        paddingVertical: 12,
                        borderBottomLeftRadius: 16,
                        borderBottomRightRadius: 16,
                        alignItems: "center",
                      }}
                    >
                      <Text
                        style={{
                          color: selectedTab === subcat.id ? "#232325" : "#fff",
                          borderWidth: selectedTab === subcat.id ? 1 : 1,
                          borderColor: selectedTab === subcat.id ? "#fff" : "transparent",
                          backgroundColor: selectedTab === subcat.id ? "#fff" : "transparent",
                          borderRadius: 16,
                          paddingHorizontal: 4,
                          paddingVertical: 4,
                          fontWeight: "bold",
                          fontSize: 15,
                          textShadowColor: "#000",
                          textShadowRadius: 2,
                        }}
                        numberOfLines={2}
                      >
                        {subcat.title}
                      </Text>
                    </LinearGradient>
                  </ImageBackground>
                </TouchableOpacity>
              ),
            )}
          </ScrollView>
        </View>
        {selectedTab &&
          tabStreams &&
          tabStreams.page &&
          renderStreamsGrid(tabStreams.page, bookmarkRefresh)}
        {!selectedTab &&
          allStreams &&
          allStreams.page &&
          renderStreamsGrid(allStreams.page, bookmarkRefresh)}
      </View>
    );
  };

  const renderSubcategoryView = () => {
    const subcatList =
      subcategories[selectedCategory ? selectedCategory : ""] || [];
    const subcatTitle = subcatList.find(
      (s: { id: string; title: string }) => s.id === selectedSubcategory,
    )?.title;
    return (
      <View style={{ flex: 1 }}>
        <View
          style={{
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            padding: 16,
          }}
        >
          <Text style={{ color: "#fff", fontSize: 22, fontWeight: "bold" }}>
            {subcatTitle}
          </Text>
          <TouchableOpacity
            style={{
              backgroundColor: isSubcategoryFollowed ? "#232325" : "#fff",
              borderRadius: 20,
              paddingHorizontal: 16,
              paddingVertical: 6,
              borderWidth: 1,
              borderColor: "#fff",
            }}
            onPress={handleFollowToggle}
          >
            <Text
              style={{
                color: isSubcategoryFollowed ? "#fff" : "#232325",
                fontWeight: "bold",
              }}
            >
              {isSubcategoryFollowed ? "Unfollow" : "Follow"}
            </Text>
          </TouchableOpacity>
        </View>
        {subcategoryStreams &&
          subcategoryStreams.page &&
          renderStreamsGrid(subcategoryStreams.page, bookmarkRefresh)}
      </View>
    );
  };

  if (selectedCategory && selectedSubcategory === "all") {
    return (
      <SafeAreaView edges={["top"]} style={styles.container}>
        <View style={styles.fixedHeader} onLayout={handleHeaderLayout}>
          <Header
            back
            onBack={() => {
              setSelectedCategory(null);
              setSelectedSubcategory(null);
              setSelectedTab(null);
            }}
            isSearchActive={isSearchActive}
            onSearchActiveChange={setIsSearchActive}
          />
        </View>
        {renderAllView()}
      </SafeAreaView>
    );
  }

  if (
    selectedCategory &&
    selectedSubcategory &&
    selectedSubcategory !== "all"
  ) {
    return (
      <SafeAreaView edges={["top"]} style={styles.container}>
        <View style={styles.fixedHeader} onLayout={handleHeaderLayout}>
          <Header
            back
            onBack={() => {
              setSelectedCategory(null);
              setSelectedSubcategory(null);
              setSelectedTab(null);
            }}
            isSearchActive={isSearchActive}
            onSearchActiveChange={setIsSearchActive}
          />
        </View>
        {renderSubcategoryView()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView edges={["top"]} style={styles.container}>
      <View style={styles.fixedHeader} onLayout={handleHeaderLayout}>
        <Header
          isSearchActive={isSearchActive}
          onSearchActiveChange={setIsSearchActive}
        />
      </View>
      {!isSearchActive && (
        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {isLoading ? (
            <CategoryGridSkeleton />
          ) : (
            (() => {
              const items = [];
              let currentRow = [];
              for (let i = 0; i < categories.length; i++) {
                const category = categories[i];
                if (!category) continue;
                const isSelected = category.id === selectedCategory;
                currentRow.push(
                  <TouchableOpacity
                    key={category.id}
                    style={[styles.card, isSelected && styles.cardSelected]}
                    onPress={() =>
                      handleCategoryPress(category.id, Math.floor(i / 3))
                    }
                  >
                    <View
                      style={[
                        styles.imageContainer,
                        isSelected && styles.imageContainerSelected,
                      ]}
                    >
                      <Image
                        source={{ uri: getImageUrl(category.image) }}
                        style={styles.categoryImage}
                        resizeMode="contain"
                      />
                    </View>
                    <Text style={styles.categoryTitle} numberOfLines={2}>
                      {category.title}
                    </Text>
                  </TouchableOpacity>,
                );
                if (currentRow.length === 3 || i === categories.length - 1) {
                  const rowIndex = Math.floor(i / 3);
                  items.push(
                    <View
                      key={`row-${rowIndex}`}
                      style={styles.gridRow}
                      onLayout={(event) => handleRowLayout(rowIndex, event)}
                    >
                      {currentRow}
                    </View>,
                  );
                  if (
                    selectedCategory &&
                    currentRow.some((item) => item.key === selectedCategory)
                  ) {
                    items.push(
                      <View key={`subcategories-${selectedCategory}`}>
                        {isLoading ? (
                          <SubcategoriesSkeleton />
                        ) : (
                          renderSubcategories(selectedCategory)
                        )}
                      </View>,
                    );
                  }
                  currentRow = [];
                }
              }
              return items;
            })()
          )}
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  fixedHeader: {
    backgroundColor: "#1C1C1E",
    zIndex: 1,
  },
  fixedTabs: {
    backgroundColor: "#1C1C1E",
    zIndex: 1,
  },
  tabsContainer: {
    flexDirection: "row",
    padding: 10,
    gap: 10,
  },
  tab: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: "#1c1c1e",
  },
  tabActive: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: "#fff",
  },
  tabText: {
    color: "#fff",
    fontSize: 14,
  },
  tabTextActive: {
    color: "#000",
    fontSize: 14,
    fontWeight: "600",
  },
  scrollContent: {
    paddingTop: 10,
  },
  scrollView: {
    flex: 1,
  },
  gridRow: {
    flexDirection: "row",
    padding: 5,
  },
  card: {
    flex: 1,
    margin: 5,
  },
  cardSelected: {
  },
  imageContainer: {
    aspectRatio: 1,
    backgroundColor: "#1c1c1e",
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#2C2C2E",
    overflow: "hidden",
  },
  imageContainerSelected: {
    backgroundColor: "#1a96d2",
    borderColor: "#fff",
  },
  imagePlaceholder: {
    color: "#666",
    fontSize: 14,
  },
  categoryTitle: {
    color: "#fff",
    fontSize: 13,
    marginTop: 8,
    marginBottom: 4,
  },
  viewersContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    height: 20,
  },
  liveIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#ff0000",
  },
  viewersText: {
    color: "#999",
    fontSize: 12,
  },
  subcategoriesSection: {
    width: "100%",
    paddingHorizontal: 10,
  },
  allCategoryItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#1c1c1e",
  },
  subcategoryItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#1c1c1e",
  },
  subcategoryIcon: {
    width: 40,
    height: 40,
    backgroundColor: "#1c1c1e",
    borderRadius: 8,
    overflow: "hidden",
    marginRight: 12,
  },
  subcategoryImage: {
    width: "100%",
    height: "100%",
  },
  subcategoryContent: {
    flex: 1,
  },
  subcategoryTitle: {
    color: "#fff",
    fontSize: 15,
    marginBottom: 4,
  },
  categoryImage: {
    width: "100%",
    height: "100%",
  },
});
