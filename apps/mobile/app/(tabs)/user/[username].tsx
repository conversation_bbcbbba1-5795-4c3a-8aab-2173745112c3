import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Share,
  TextInput,
  Pressable,
  Modal,
  ActivityIndicator,
  StatusBar,
  FlatList,
  Dimensions,
  useWindowDimensions,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { useRouter, useLocalSearchParams } from "expo-router";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id, Doc } from "../../../convex/_generated/dataModel";
import { Image as ExpoImage } from "expo-image";
import { useState, useEffect, useRef, useCallback } from "react";
import UserSearch from "../../../components/ui/user-search";
import { useUser } from "../../../hooks/useUser";
import { ReportModal } from "../../../components/ui/ReportModal";
import { Tab<PERSON>iew, SceneMap, TabBar } from "react-native-tab-view";
import UserProfileSkeleton from "../../../components/skeletons/UserProfileSkeleton";
import ShopSkeleton from "../../../components/skeletons/ShopSkeleton";
import ShowsSkeleton from "../../../components/skeletons/ShowsSkeleton";

type ProductWithUrls = Doc<"products"> & { imageUrls: string[] };

type Route = {
  key: string;
  title: string;
};

interface ReviewWithReviewer {
  _id: string;
  _creationTime: number;
  overallRating: number;
  shippingRating?: number;
  packagingRating?: number;
  accuracyRating?: number;
  reviewText: string;
  replyText?: string;
  reviewer?: {
    _id: string;
    name?: string;
    avatarUrl?: string | null;
    username?: string;
  };
}

const renderTabBar = (props: any) => (
  <TabBar
    {...props}
    indicatorStyle={{ backgroundColor: "#fff" }}
    style={{ backgroundColor: "#2C2C2E" }}
    labelStyle={{ color: "#fff", fontSize: 16, textTransform: "none" }}
    pressColor="transparent"
    tabStyle={{ width: Dimensions.get("window").width / 4 }}
    scrollEnabled={false}
  />
);

export default function UserProfileScreen() {
  const router = useRouter();
  const { username, searchQuery, chatId } = useLocalSearchParams<{
    username: string;
    searchQuery?: string;
    chatId?: Id<"chats">;
  }>();
  const [showSearch, setShowSearch] = useState(false);
  const { user: currentUser } = useUser();
  const profileUser = useQuery(api.users.getUserByUsername, { username });
  const userId = profileUser?._id;
  const isFollowing = useQuery(api.users.isFollowing, 
    userId ? { targetUserId: userId } : "skip"
  );
  const followStats = useQuery(api.users.getFollowCounts, 
    userId ? { userId: userId } : "skip"
  );
  const followUser = useMutation(api.users.followUser);
  const unfollowUser = useMutation(api.users.unfollowUser);
  const [isBioExpanded, setIsBioExpanded] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const hasBlocked = useQuery(api.users.hasBlocked, 
    userId ? { targetUserId: userId } : "skip"
  );
  const isBlocked = useQuery(api.users.isBlocked, 
    userId ? { targetUserId: userId } : "skip"
  );
  const blockUser = useMutation(api.users.blockUser);
  const unblockUser = useMutation(api.users.unblockUser);
  const createChat = useMutation(api.chat.createChat);
  const [isCreatingChat, setIsCreatingChat] = useState(false);
  const existingChats = useQuery(api.chat.listChats, 
    userId ? { userId: userId } : "skip"
  );
  const [isReportModalVisible, setIsReportModalVisible] = useState(false);
  const userProducts = useQuery(api.users.getUserProducts, 
    userId ? { userId: userId, status: "active" } : "skip"
  );
  const userStreams = useQuery(api.streams.listByUser, 
    userId ? {} : "skip"
  );
  const layout = useWindowDimensions();

  const [index, setIndex] = useState(0);
  const [routes] = useState<Route[]>([
    { key: "shop", title: "Shop" },
    { key: "shows", title: "Shows" },
    { key: "reviews", title: "Reviews" },
    // { key: 'clips', title: 'Clips' },
  ]);

  const isOwnProfile = currentUser?._id && userId ? currentUser._id.toString() === userId.toString() : false;

  const userReviews = useQuery(api.users.getUserReviews, 
    userId ? { reviewedUserId: userId, limit: 5 } : "skip"
  ) as ReviewWithReviewer[] | undefined;
  const createReview = useMutation(api.users.createUserReview);
  // Review form state
  const [overallRating, setOverallRating] = useState<number>(5);
  const [shippingRating, setShippingRating] = useState<number | undefined>(
    undefined,
  );
  const [packagingRating, setPackagingRating] = useState<number | undefined>(
    undefined,
  );
  const [accuracyRating, setAccuracyRating] = useState<number | undefined>(
    undefined,
  );
  const [reviewText, setReviewText] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [reviewModalVisible, setReviewModalVisible] = useState(false);

  // Check if current user can review
  const hasReviewed = !!(
    userReviews &&
    currentUser &&
    userReviews.some((r) => r.reviewer?._id === currentUser._id)
  );
  const canReview = currentUser && !isOwnProfile && !hasReviewed;

  const handleBack = () => {
    if (chatId) {
      // If we came from a chat, go back to that chat
      router.push(`/chat/${chatId}`);
    } else if (searchQuery) {
      // If we came from search, show the search modal
      setShowSearch(true);
    } else {
      try {
        router.back();
      } catch (error) {
        // If back navigation fails, redirect to home tab
        router.replace("/");
      }
    }
  };

  const handleShare = async () => {
    try {
      // Create a deep link URL using the app scheme
      const deepLink = `liveciety://user/${userId}`;
      // Create a web fallback URL
      const webUrl = `https://liveciety.com/user/${profileUser?.username}`;

      const result = await Share.share({
        message: `Check out ${profileUser?.username}'s profile on Liveciety!`,
        url: deepLink,
        title: "Share Profile",
      });
    } catch (error) {
      console.error(error);
    }
  };

  const handleFollowToggle = async () => {
    try {
      if (isFollowing) {
        await unfollowUser({ targetUserId: userId as Id<"users"> });
      } else {
        await followUser({ targetUserId: userId as Id<"users"> });
      }
    } catch (error) {
      console.error("Error toggling follow:", error);
    }
  };

  const handleBlockToggle = async () => {
    try {
      if (hasBlocked) {
        await unblockUser({ targetUserId: userId as Id<"users"> });
      } else {
        await blockUser({ targetUserId: userId as Id<"users"> });
      }
      setShowOptions(false);
    } catch (error) {
      console.error("Error toggling block:", error);
    }
  };

  const handleMessage = async () => {
    if (!profileUser || !existingChats) return;

    try {
      setIsCreatingChat(true);

      // Check if we already have a chat with this user
      const existingChat = existingChats.find(
        (chat: any) =>
          !chat.isGroup &&
          chat.participants.length === 2 &&
          chat.participants.some((p: any) => p._id === profileUser._id),
      );

      if (existingChat) {
        // If chat exists, navigate to it with the user ID for back navigation
        router.push(`/chat/${existingChat._id}?id=${userId}`);
      } else {
        // If no chat exists, create a new one
        const chatId = await createChat({
          participantIds: [profileUser._id],
          isGroup: false,
        });
        router.push(`/chat/${chatId}?id=${userId}`);
      }
    } catch (error) {
      console.error("Error handling chat:", error);
    } finally {
      setIsCreatingChat(false);
    }
  };

  const handleSubmitReview = async () => {
    setError(null);
    setSuccess(null);
    if (!overallRating || !reviewText.trim()) {
      setError("Overall rating and review text are required.");
      return;
    }
    setSubmitting(true);
    try {
      await createReview({
        reviewedUserId: userId as Id<"users">,
        overallRating,
        shippingRating,
        packagingRating,
        accuracyRating,
        reviewText,
      });
      setSuccess("Review submitted!");
      setReviewText("");
      setOverallRating(5);
      setShippingRating(undefined);
      setPackagingRating(undefined);
      setAccuracyRating(undefined);
      setReviewModalVisible(false);
    } catch (e: any) {
      setError(e?.message || "Failed to submit review");
    } finally {
      setSubmitting(false);
    }
  };

  const renderProduct = ({ item: product }: { item: ProductWithUrls }) => (
    <TouchableOpacity
      style={styles.productCard}
      onPress={() =>
        router.push({
          pathname: "/screens/app/item/[id]",
          params: { id: product._id },
        })
      }
    >
      {product.imageUrls[0] ? (
        <ExpoImage
          source={{ uri: product.imageUrls[0] }}
          style={styles.productImage}
          contentFit="cover"
        />
      ) : (
        <View style={[styles.productImage, styles.noImage]}>
          <Ionicons name="image-outline" size={32} color="#666" />
        </View>
      )}
      <View style={styles.productInfo}>
        <Text style={styles.productName} numberOfLines={2}>
          {product.name}
        </Text>
        <Text style={styles.productPrice}>${product.price?.toFixed(2)}</Text>
        {product.inventory && product.inventory <= 5 && (
          <Text style={styles.lowStock}>Only {product.inventory} left</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderShopTab = useCallback(() => {
    if (userProducts === undefined) {
      return <ShopSkeleton />;
    }
    if (userProducts === null || userProducts.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Ionicons name="cart-outline" size={48} color="#666" />
          <Text style={styles.emptyStateText}>No items in shop</Text>
        </View>
      );
    }
    return (
      <View style={styles.tabContent}>
        <FlatList<ProductWithUrls>
          data={userProducts.filter(
            (p: ProductWithUrls | null): p is ProductWithUrls =>
              p !== null &&
              Array.isArray(p.imageUrls) &&
              p.imageUrls.every((url: string) => typeof url === "string"),
          )}
          renderItem={renderProduct}
          keyExtractor={(item) => item._id}
          numColumns={2}
          columnWrapperStyle={styles.productRow}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.productList}
          scrollEnabled={false}
        />
      </View>
    );
  }, [userProducts]);

  const renderShowsTab = useCallback(() => {
    if (userStreams === undefined) {
      return <ShowsSkeleton />;
    }
    let streams: any[] = [];
    if (
      userStreams &&
      typeof userStreams === "object" &&
      "page" in userStreams &&
      Array.isArray(userStreams.page)
    ) {
      streams = userStreams.page;
    } else if (Array.isArray(userStreams)) {
      streams = userStreams;
    }
    if (!streams || streams.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Ionicons name="tv-outline" size={48} color="#666" />
          <Text style={styles.emptyStateText}>No shows yet</Text>
        </View>
      );
    }
    return (
      <View style={styles.tabContent}>
        <FlatList
          data={streams}
          keyExtractor={(item) => item._id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.showCard}
              onPress={() => router.push(`/screens/stream/${item._id}`)}
            >
              {item.thumbnailUrl ? (
                <ExpoImage
                  source={{ uri: item.thumbnailUrl }}
                  style={styles.showThumbnail}
                  contentFit="cover"
                />
              ) : (
                <View style={[styles.showThumbnail, styles.noImage]}>
                  <Ionicons name="tv-outline" size={32} color="#666" />
                </View>
              )}
              <View style={styles.showInfo}>
                <Text style={styles.showTitle} numberOfLines={2}>
                  {item.title}
                </Text>
                {item.category && (
                  <Text style={styles.showCategory}>{item.category}</Text>
                )}
              </View>
            </TouchableOpacity>
          )}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.productList}
          scrollEnabled={false}
        />
      </View>
    );
  }, [userStreams, router]);

  const renderReviewsTab = useCallback(() => {
    // Local state for expanded reviews
    const [expandedReviews, setExpandedReviews] = useState<{
      [key: string]: boolean;
    }>({});
    const toggleExpand = (id: string) => {
      setExpandedReviews((prev) => ({ ...prev, [id]: !prev[id] }));
    };
    return (
      <View style={styles.tabContent}>
        {/* Write a Review Button */}
        <TouchableOpacity
          style={{
            backgroundColor: "#1a96d2",
            borderRadius: 8,
            padding: 12,
            alignItems: "center",
            marginBottom: 24,
          }}
          onPress={() => setReviewModalVisible(true)}
        >
          <Text style={{ color: "#000", fontWeight: "bold", fontSize: 16 }}>
            Write a Review
          </Text>
        </TouchableOpacity>
        {/* Reviews List */}
        {userReviews === undefined ? (
          <Text style={styles.emptyStateText}>Loading reviews...</Text>
        ) : userReviews.length === 0 ? (
          <Text style={styles.emptyStateText}>No reviews yet</Text>
        ) : (
          userReviews.map((review) => {
            const reviewDate = new Date(review._creationTime);
            const formattedDate = `${reviewDate.getMonth() + 1}/${reviewDate.getDate()}/${String(reviewDate.getFullYear()).slice(-2)}`;
            const isLong = review.reviewText.length > 80;
            const expanded = expandedReviews[review._id] || false;
            return (
              <View key={review._id} style={styles.reviewCard}>
                {/* Reviewer Row */}
                <View style={styles.reviewerRow}>
                  {review.reviewer?.avatarUrl ? (
                    <ExpoImage
                      source={{ uri: review.reviewer.avatarUrl }}
                      style={styles.reviewerAvatar}
                      contentFit="cover"
                    />
                  ) : (
                    <View style={styles.reviewerAvatarFallback}>
                      <Text style={styles.reviewerAvatarText}>
                        {review.reviewer?.name?.[0]?.toUpperCase() || "?"}
                      </Text>
                    </View>
                  )}
                  <Text style={styles.reviewerName}>
                    {review.reviewer?.username || "User"}
                  </Text>
                  <Text style={styles.reviewDate}>{formattedDate}</Text>
                </View>
                {/* Star Row */}
                <View style={styles.starRow}>
                  <Text style={styles.overallRating}>
                    {review.overallRating.toFixed(1)}
                  </Text>
                  <Ionicons name="star" size={18} color="#1a96d2" />
                  <Text style={styles.starLabel}>Greatness</Text>
                </View>
                {/* Review Text */}
                <Text style={styles.reviewText}>
                  {expanded || !isLong
                    ? review.reviewText
                    : review.reviewText.slice(0, 80) + "..."}
                </Text>
                <TouchableOpacity onPress={() => toggleExpand(review._id)}>
                  <Text style={styles.seeMore}>
                    {expanded ? "See Less" : "See More"}
                  </Text>
                </TouchableOpacity>
                {/* Sub-Ratings Table only when expanded */}
                {expanded && (
                  <View style={styles.subRatingsTable}>
                    {[
                      { label: "Overall", value: review.overallRating },
                      { label: "Shipping", value: review.shippingRating },
                      { label: "Packaging", value: review.packagingRating },
                      { label: "Accuracy", value: review.accuracyRating },
                    ].map(
                      (item, idx, arr) =>
                        item.value !== undefined && (
                          <View key={item.label}>
                            <View style={styles.subRatingRow}>
                              <Text style={styles.subRatingLabel}>
                                {item.label}
                              </Text>
                              <View style={styles.subRatingValueContainer}>
                                <Text style={styles.subRatingValue}>
                                  {item.value.toFixed(1)}
                                </Text>
                                <Ionicons
                                  name="star"
                                  size={16}
                                  color="#1a96d2"
                                  style={{ marginLeft: 2 }}
                                />
                              </View>
                            </View>
                            {idx < arr.length - 1 && (
                              <View style={styles.subRatingDivider} />
                            )}
                          </View>
                        ),
                    )}
                  </View>
                )}
                {review.replyText && (
                  <View
                    style={{
                      backgroundColor: "#222",
                      borderRadius: 8,
                      padding: 8,
                      marginTop: 4,
                    }}
                  >
                    <Text style={{ color: "#1a96d2", fontWeight: "bold" }}>
                      Seller reply:
                    </Text>
                    <Text style={{ color: "#fff" }}>{review.replyText}</Text>
                  </View>
                )}
              </View>
            );
          })
        )}
      </View>
    );
  }, [
    canReview,
    userReviews,
    overallRating,
    shippingRating,
    packagingRating,
    accuracyRating,
    reviewText,
    submitting,
    error,
    success,
    currentUser,
    isOwnProfile,
  ]);

  const renderScene = SceneMap({
    shop: renderShopTab,
    shows: renderShowsTab,
    reviews: renderReviewsTab,
    // clips: renderEmptyTab("videocam-outline", "No clips yet"),
  });

  if (isBlocked) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.headerContainer}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Ionicons name="chevron-back" size={24} color="#fff" />
          </TouchableOpacity>
        </View>
        <View style={styles.blockedContainer}>
          <Ionicons name="ban" size={64} color="#666" />
          <Text style={styles.blockedText}>
            You have been blocked by this user
          </Text>
          <TouchableOpacity onPress={handleBack} style={styles.blockedButton}>
            <Text style={styles.blockedButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  if (!profileUser) {
    return <UserProfileSkeleton />;
  }

  return (
    <SafeAreaView style={styles.container} edges={["top"]}>
      <StatusBar barStyle="light-content" backgroundColor="#1C1C1E" />
      {showSearch && (
        <UserSearch
          initialActive={showSearch}
          initialQuery={searchQuery}
          onClose={() => {
            setShowSearch(false);
            router.back();
          }}
        />
      )}

      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        {/* Header Section */}
        <View style={styles.headerBackground}>
          {profileUser?.coverImageUrl && (
            <ExpoImage
              source={{ uri: profileUser.coverImageUrl }}
              style={StyleSheet.absoluteFillObject}
              contentFit="cover"
            />
          )}
          <View style={styles.headerOverlay} />
          <View style={styles.headerContainer}>
            <TouchableOpacity onPress={handleBack} style={styles.backButton}>
              <Ionicons name="chevron-back" size={24} color="#fff" />
            </TouchableOpacity>
            <View style={styles.headerButtons}>
              <TouchableOpacity onPress={handleShare} style={styles.iconButton}>
                <Ionicons name="share-outline" size={24} color="#fff" />
              </TouchableOpacity>
              {!isOwnProfile && (
                <TouchableOpacity
                  style={styles.iconButton}
                  onPress={() => setShowOptions(true)}
                >
                  <Ionicons name="ellipsis-horizontal" size={24} color="#fff" />
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>

        {/* Profile Info */}
        <View style={styles.profileInfo}>
          <View style={styles.avatarContainer}>
            {profileUser?.avatarUrl ? (
              <ExpoImage
                source={{ uri: profileUser.avatarUrl }}
                style={styles.avatar}
                contentFit="cover"
              />
            ) : (
              <View style={styles.avatarFallback}>
                <Text style={styles.avatarText}>
                  {profileUser?.name?.[0]?.toUpperCase() || "U"}
                </Text>
              </View>
            )}
          </View>
          <Text style={styles.username}>{profileUser?.username}</Text>
          <Text style={styles.name}>{profileUser?.name}</Text>

          {/* Stats */}
          <View style={styles.statsContainer}>
            <TouchableOpacity
              style={styles.stat}
              onPress={() =>
                router.push(`/user/${userId}/followers?initialTab=followers`)
              }
            >
              <Text style={styles.statValue}>
                {followStats?.followers || 0}
              </Text>
              <Text style={styles.statLabel}>Followers</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.stat}
              onPress={() =>
                router.push(`/user/${userId}/followers?initialTab=following`)
              }
            >
              <Text style={styles.statValue}>
                {followStats?.following || 0}
              </Text>
              <Text style={styles.statLabel}>Following</Text>
            </TouchableOpacity>
          </View>

          {/* Bio */}
          {profileUser?.bio && (
            <View style={styles.bioContainer}>
              <Text
                style={styles.bio}
                numberOfLines={isBioExpanded ? undefined : 2}
              >
                {profileUser.bio}
              </Text>
              {profileUser.bio.length > 80 && (
                <TouchableOpacity
                  style={styles.showMoreButton}
                  onPress={() => setIsBioExpanded(!isBioExpanded)}
                >
                  <Text style={styles.showMoreText}>
                    {isBioExpanded ? "Show less" : "Show more"}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          )}

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            {isOwnProfile ? (
              <>
                <TouchableOpacity
                  style={styles.messageButton}
                  onPress={() => router.push("/screens/app/edit-profile")}
                >
                  <Ionicons name="pencil-outline" size={20} color="#fff" />
                  <Text style={styles.messageButtonText}>Edit Profile</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.messageButton}
                  onPress={() => router.push("/(tabs)/activity?tab=messages")}
                >
                  <Ionicons name="chatbubble-outline" size={20} color="#fff" />
                  <Text style={styles.messageButtonText}>Messages</Text>
                </TouchableOpacity>
              </>
            ) : (
              <>
                <TouchableOpacity
                  style={[
                    styles.followButton,
                    isFollowing ? styles.followingButton : null,
                  ]}
                  onPress={handleFollowToggle}
                >
                  <Ionicons
                    name={isFollowing ? "checkmark" : "person-add-outline"}
                    size={20}
                    color={isFollowing ? "#fff" : "#000"}
                  />
                  <Text
                    style={[
                      styles.followButtonText,
                      isFollowing ? styles.followingButtonText : null,
                    ]}
                  >
                    {isFollowing ? "Following" : "Follow"}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.messageButton}
                  onPress={handleMessage}
                  disabled={isCreatingChat}
                >
                  {isCreatingChat ? (
                    <ActivityIndicator color="#fff" size="small" />
                  ) : (
                    <>
                      <Ionicons
                        name="chatbubble-outline"
                        size={20}
                        color="#fff"
                      />
                      <Text style={styles.messageButtonText}>Message</Text>
                    </>
                  )}
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>

        {/* Tab View */}
        <View style={styles.tabViewContainer}>
          <TabView
            navigationState={{ index, routes }}
            renderScene={renderScene}
            onIndexChange={setIndex}
            initialLayout={{ width: layout.width }}
            renderTabBar={renderTabBar}
            style={styles.tabView}
          />
        </View>
      </ScrollView>

      <Modal
        visible={showOptions}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowOptions(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowOptions(false)}
        >
          <View style={styles.optionsContainer}>
            {!isOwnProfile && (
              <>
                <TouchableOpacity
                  style={styles.optionButton}
                  onPress={() => {
                    setShowOptions(false);
                    setIsReportModalVisible(true);
                  }}
                >
                  <Ionicons name="flag-outline" size={24} color="#ff4444" />
                  <Text style={styles.optionText}>Report User</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.optionButton}
                  onPress={handleBlockToggle}
                >
                  <Ionicons
                    name={hasBlocked ? "ban-outline" : "ban"}
                    size={24}
                    color={hasBlocked ? "#666" : "#ff4444"}
                  />
                  <Text
                    style={[
                      styles.optionText,
                      hasBlocked && styles.unblockText,
                    ]}
                  >
                    {hasBlocked ? "Unblock User" : "Block User"}
                  </Text>
                </TouchableOpacity>
              </>
            )}
            <TouchableOpacity
              style={styles.optionButton}
              onPress={() => setShowOptions(false)}
            >
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
      <ReportModal
        isVisible={isReportModalVisible}
        onClose={() => setIsReportModalVisible(false)}
        contentType="user"
        contentId={userId as Id<"users">}
        reportedUserId={userId as Id<"users">}
      />
      <Modal
        visible={reviewModalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setReviewModalVisible(false)}
      >
        <View
          style={{
            flex: 1,
            backgroundColor: "rgba(0,0,0,0.7)",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <View
            style={{
              width: "90%",
              backgroundColor: "#18181A",
              borderRadius: 16,
              padding: 20,
            }}
          >
            <Text
              style={{
                color: "#fff",
                fontWeight: "bold",
                fontSize: 18,
                marginBottom: 12,
              }}
            >
              Write a Review
            </Text>
            {canReview ? (
              <>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    marginBottom: 8,
                  }}
                >
                  <Text style={{ color: "#1a96d2", marginRight: 8 }}>
                    Overall:
                  </Text>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <TouchableOpacity
                      key={star}
                      onPress={() => setOverallRating(star)}
                    >
                      <Ionicons
                        name={star <= overallRating ? "star" : "star-outline"}
                        size={24}
                        color="#1a96d2"
                      />
                    </TouchableOpacity>
                  ))}
                </View>
                <View style={{ flexDirection: "row", gap: 8, marginBottom: 8 }}>
                  <View style={{ flex: 1 }}>
                    <Text style={{ color: "#1a96d2" }}>Shipping</Text>
                    <TextInput
                      style={{
                        color: "#fff",
                        backgroundColor: "#222",
                        borderRadius: 6,
                        padding: 4,
                        marginTop: 2,
                      }}
                      placeholder="1-5"
                      placeholderTextColor="#888"
                      keyboardType="numeric"
                      value={
                        shippingRating === undefined
                          ? ""
                          : String(shippingRating)
                      }
                      onChangeText={(t) =>
                        setShippingRating(
                          t ? Math.max(1, Math.min(5, parseInt(t))) : undefined,
                        )
                      }
                      maxLength={1}
                    />
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={{ color: "#1a96d2" }}>Packaging</Text>
                    <TextInput
                      style={{
                        color: "#fff",
                        backgroundColor: "#222",
                        borderRadius: 6,
                        padding: 4,
                        marginTop: 2,
                      }}
                      placeholder="1-5"
                      placeholderTextColor="#888"
                      keyboardType="numeric"
                      value={
                        packagingRating === undefined
                          ? ""
                          : String(packagingRating)
                      }
                      onChangeText={(t) =>
                        setPackagingRating(
                          t ? Math.max(1, Math.min(5, parseInt(t))) : undefined,
                        )
                      }
                      maxLength={1}
                    />
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text style={{ color: "#1a96d2" }}>Accuracy</Text>
                    <TextInput
                      style={{
                        color: "#fff",
                        backgroundColor: "#222",
                        borderRadius: 6,
                        padding: 4,
                        marginTop: 2,
                      }}
                      placeholder="1-5"
                      placeholderTextColor="#888"
                      keyboardType="numeric"
                      value={
                        accuracyRating === undefined
                          ? ""
                          : String(accuracyRating)
                      }
                      onChangeText={(t) =>
                        setAccuracyRating(
                          t ? Math.max(1, Math.min(5, parseInt(t))) : undefined,
                        )
                      }
                      maxLength={1}
                    />
                  </View>
                </View>
                <TextInput
                  style={{
                    color: "#fff",
                    backgroundColor: "#222",
                    borderRadius: 6,
                    padding: 8,
                    minHeight: 60,
                    marginBottom: 8,
                  }}
                  placeholder="Write your review..."
                  placeholderTextColor="#888"
                  value={reviewText}
                  onChangeText={setReviewText}
                  multiline
                  editable={!submitting}
                />
                {error && (
                  <Text style={{ color: "#FF453A", marginBottom: 4 }}>
                    {error}
                  </Text>
                )}
                {success && (
                  <Text style={{ color: "#4CD964", marginBottom: 4 }}>
                    {success}
                  </Text>
                )}
                <TouchableOpacity
                  style={{
                    backgroundColor: submitting ? "#444" : "#1a96d2",
                    borderRadius: 8,
                    padding: 12,
                    alignItems: "center",
                    marginTop: 4,
                  }}
                  onPress={handleSubmitReview}
                  disabled={submitting}
                >
                  <Text
                    style={{ color: "#000", fontWeight: "bold", fontSize: 16 }}
                  >
                    {submitting ? "Submitting..." : "Submit Review"}
                  </Text>
                </TouchableOpacity>
              </>
            ) : (
              <Text
                style={{
                  color: "#fff",
                  textAlign: "center",
                  marginVertical: 24,
                }}
              >
                {isOwnProfile
                  ? "You can't review your own profile."
                  : hasReviewed
                    ? "You have already reviewed this user."
                    : "You must be logged in to review."}
              </Text>
            )}
            <TouchableOpacity
              style={{ marginTop: 16, alignItems: "center" }}
              onPress={() => setReviewModalVisible(false)}
            >
              <Text style={{ color: "#1a96d2", fontWeight: "bold" }}>
                Close
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
  },
  scrollContainer: {
    flex: 1,
  },
  headerBackground: {
    height: 200,
    backgroundColor: "#1a1a1a",
    position: "relative",
  },
  headerOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0,0,0,0.4)",
  },
  headerContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  headerButtons: {
    flexDirection: "row",
    gap: 8,
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  profileInfo: {
    alignItems: "center",
    paddingHorizontal: 16,
    marginTop: -50,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: "#333",
    overflow: "hidden",
    marginBottom: 16,
  },
  avatar: {
    width: "100%",
    height: "100%",
  },
  avatarFallback: {
    width: "100%",
    height: "100%",
    backgroundColor: "#666",
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "#fff",
    fontSize: 40,
    fontWeight: "bold",
  },
  username: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 4,
  },
  name: {
    fontSize: 16,
    color: "#8E8E93",
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "100%",
    marginBottom: 24,
    borderWidth: 1,
    borderColor: "#2C2C2E",
    padding: 16,
    borderRadius: 12,
    backgroundColor: "#222",
  },
  stat: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    gap: 8,
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
  },
  statLabel: {
    fontSize: 18,
    color: "#8E8E93",
  },
  bioContainer: {
    width: "100%",
    paddingHorizontal: 16,
    marginBottom: 24,
  },
  bio: {
    fontSize: 14,
    color: "#fff",
    textAlign: "left",
  },
  showMoreButton: {
    alignSelf: "flex-end",
    marginTop: 8,
    padding: 8,
    backgroundColor: "#333",
    borderRadius: 8,
  },
  showMoreText: {
    color: "#fff",
    fontSize: 14,
  },
  actionButtons: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 24,
  },
  followButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1a96d2",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  followingButton: {
    backgroundColor: "#333",
  },
  followButtonText: {
    color: "#000",
    fontWeight: "600",
    fontSize: 16,
  },
  followingButtonText: {
    color: "#fff",
  },
  messageButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#333",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  messageButtonText: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "flex-end",
  },
  optionsContainer: {
    backgroundColor: "#1C1C1E",
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    padding: 16,
    gap: 8,
  },
  optionButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    gap: 12,
    borderRadius: 8,
    backgroundColor: "#2C2C2E",
  },
  optionText: {
    color: "#ff4444",
    fontSize: 16,
    fontWeight: "600",
  },
  unblockText: {
    color: "#666",
  },
  cancelText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center",
  },
  blockedContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
    padding: 32,
  },
  blockedText: {
    color: "#666",
    fontSize: 18,
    textAlign: "center",
  },
  blockedButton: {
    backgroundColor: "#333",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  blockedButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#fff",
    fontSize: 16,
  },
  tabViewContainer: {
    height: 500, // Adjust this value based on your needs
  } as const,
  tabView: {
    backgroundColor: "#1C1C1E",
  } as const,
  tabContent: {
    minHeight: 400,
    paddingTop: 16,
  } as const,
  emptyState: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
    gap: 16,
  },
  emptyStateText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
  moreButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  productList: {
    padding: 8,
  },
  productRow: {
    justifyContent: "space-between",
  },
  productCard: {
    width: (Dimensions.get("window").width - 32) / 2 - 8,
    backgroundColor: "#1C1C1E",
    borderRadius: 12,
    marginBottom: 16,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#2C2C2E",
  },
  productImage: {
    width: "100%",
    height: 200,
    backgroundColor: "#2C2C2E",
  },
  productInfo: {
    padding: 12,
  },
  productName: {
    fontSize: 14,
    fontWeight: "500",
    color: "#fff",
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1a96d2",
  },
  lowStock: {
    fontSize: 12,
    color: "#ff4444",
    marginTop: 4,
  },
  noImage: {
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#2C2C2E",
  },
  showCard: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1C1C1E",
    borderRadius: 12,
    marginBottom: 16,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#2C2C2E",
    padding: 8,
  },
  showThumbnail: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: "#2C2C2E",
    marginRight: 16,
  },
  showInfo: {
    flex: 1,
    justifyContent: "center",
  },
  showTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 4,
  },
  showCategory: {
    fontSize: 14,
    color: "#1a96d2",
  },
  reviewCard: {
    backgroundColor: "#18181A",
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: "#232325",
  },
  reviewerRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  reviewerAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  reviewerAvatarFallback: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#6C5CE7",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 8,
  },
  reviewerAvatarText: {
    color: "#fff",
    fontWeight: "bold",
  },
  reviewerName: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
    marginRight: 8,
  },
  reviewDate: {
    color: "#8E8E93",
    fontSize: 12,
    marginLeft: "auto",
  },
  starRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
    gap: 4,
  },
  overallRating: {
    color: "#1a96d2",
    fontWeight: "bold",
    fontSize: 18,
    marginRight: 4,
  },
  starLabel: {
    color: "#1a96d2",
    marginLeft: 8,
    fontWeight: "600",
  },
  reviewText: {
    color: "#fff",
    marginBottom: 4,
    fontSize: 15,
  },
  seeMore: {
    color: "#1a96d2",
    fontWeight: "bold",
    marginBottom: 8,
  },
  subRatingsTable: {
    marginTop: 8,
    backgroundColor: "#232325",
    borderRadius: 12,
    paddingVertical: 4,
    paddingHorizontal: 0,
  },
  subRatingRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
    paddingHorizontal: 16,
    justifyContent: "space-between",
  },
  subRatingLabel: {
    color: "#fff",
    fontSize: 15,
    flex: 1,
  },
  subRatingValueContainer: {
    flexDirection: "row",
    alignItems: "center",
    minWidth: 50,
    justifyContent: "flex-end",
  },
  subRatingValue: {
    color: "#1a96d2",
    fontWeight: "bold",
    fontSize: 16,
    marginRight: 2,
  },
  subRatingDivider: {
    height: 1,
    backgroundColor: "#353537",
    marginHorizontal: 16,
  },
});
