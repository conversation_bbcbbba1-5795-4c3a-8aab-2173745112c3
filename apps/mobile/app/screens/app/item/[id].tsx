import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Dimensions,
  Share,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { Image } from "expo-image";
import { Ionicons } from "@expo/vector-icons";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { TabView, SceneMap, TabBar } from "react-native-tab-view";

const { width } = Dimensions.get("window");

function formatDate(ts: number) {
  const d = new Date(ts);
  return `${d.getMonth() + 1}/${d.getDate()}/${d.getFullYear()}`;
}

export default function ItemProfileScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: Id<"products"> }>();
  const item = useQuery(api.products.getProduct, { productId: id as Id<"products"> });
  const seller = useQuery(
    api.users.getUser,
    item?.sellerId ? { userId: item.sellerId } : "skip",
  );
  const sellerStats = seller?.sellerProfile;
  const userReviews = useQuery(
    api.users.getUserReviews,
    item?.sellerId ? { reviewedUserId: item.sellerId, limit: 3 } : "skip",
  );
  const [tabIndex, setTabIndex] = useState(0);
  const [routes] = useState([
    { key: "details", title: "Details" },
    { key: "seller", title: "Seller Info" },
  ]);

  if (item === undefined) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1a96d2" />
      </View>
    );
  }
  if (!item) {
    return (
      <View style={styles.loadingContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#666" />
        <Text style={styles.emptyText}>Item not found</Text>
      </View>
    );
  }

  // --- Tabs ---
  const renderDetailsTab = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionLabel}>Description</Text>
      <Text style={styles.sectionValue}>
        {item.description || "No description provided."}
      </Text>
      <View style={styles.sectionRow}>
        <Text style={styles.sectionLabel}>Category</Text>
        <Text style={styles.sectionValue}>
          {item.category || "Uncategorized"}
        </Text>
      </View>
      <View style={styles.sectionRow}>
        <Text style={styles.sectionLabel}>Created on</Text>
        <Text style={styles.sectionValue}>
          {formatDate(item._creationTime)}
        </Text>
      </View>
    </View>
  );

  const renderSellerTab = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionLabel}>Store Name</Text>
      <Text style={styles.sectionValue}>
        {sellerStats?.storeName || item.sellerName}
      </Text>
      <Text style={styles.sectionLabel}>Bio</Text>
      <Text style={styles.sectionValue}>
        {sellerStats?.bio || "No bio provided."}
      </Text>
      <View style={styles.sectionRow}>
        <Text style={styles.sectionLabel}>Premier Shop</Text>
        <Ionicons
          name="ribbon-outline"
          size={18}
          color="#1a96d2"
          style={{ marginLeft: 4 }}
        />
      </View>
      <View style={styles.sectionRow}>
        <Text style={styles.sectionLabel}>Rating</Text>
        <Text style={styles.sectionValue}>
          {sellerStats?.rating?.toFixed(1) || "5.0"}
        </Text>
      </View>
      <View style={styles.sectionRow}>
        <Text style={styles.sectionLabel}>Sold</Text>
        <Text style={styles.sectionValue}>
          {sellerStats?.totalSales?.toLocaleString() || "0"}
        </Text>
      </View>
    </View>
  );

  const renderTabBar = (props: any) => (
    <TabBar
      {...props}
      indicatorStyle={{ backgroundColor: "#1a96d2" }}
      style={{ backgroundColor: "#232325" }}
      labelStyle={{
        color: "#fff",
        fontWeight: "bold",
        fontSize: 16,
        textTransform: "none",
      }}
      pressColor="#1a96d2"
    />
  );

  // --- Main Render ---
  return (
    <ScrollView style={styles.container}>
      {/* Image */}
      <View style={styles.imageContainer}>
        {item.imageUrl ? (
          <Image
            source={{ uri: item.imageUrl }}
            style={styles.image}
            contentFit="cover"
          />
        ) : (
          <View style={[styles.image, styles.noImage]}>
            <Ionicons name="image-outline" size={64} color="#666" />
          </View>
        )}
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => router.back()}
        >
          <Ionicons name="close" size={28} color="#fff" />
        </TouchableOpacity>
      </View>
      {/* Title, Price, Available */}
      <View style={styles.headerCard}>
        <Text style={styles.title}>{item.name}</Text>
        <Text style={styles.available}>{item.quantity} Available</Text>
        <Text style={styles.price}>
          ${item.price?.toFixed(2) || "0.00"}{" "}
          <Text style={styles.shipping}>+ $35.37 shipping + taxes</Text>
        </Text>
        {/* Save/Share Row */}
        <View style={styles.actionRow}>
          <TouchableOpacity style={styles.iconButton}>
            <Ionicons name="bookmark-outline" size={24} color="#fff" />
            <Text style={styles.iconLabel}>1</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() =>
              Share.share({ message: `Check out this item: ${item.name}` })
            }
          >
            <Ionicons name="share-outline" size={24} color="#fff" />
            <Text style={styles.iconLabel}>Share</Text>
          </TouchableOpacity>
        </View>
      </View>
      {/* Seller Card */}
      <View style={styles.sellerCard}>
        <View style={styles.sellerRow}>
          {seller?.avatarUrl ? (
            <Image
              source={{ uri: seller.avatarUrl }}
              style={styles.sellerAvatar}
            />
          ) : (
            <View style={styles.sellerAvatarFallback}>
              <Ionicons name="person" size={28} color="#fff" />
            </View>
          )}
          <View style={{ flex: 1 }}>
            <Text style={styles.sellerName}>{item.sellerName}</Text>
            <View style={styles.sellerStatsRow}>
              <Text style={styles.sellerStat}>
                ⭐ {sellerStats?.rating?.toFixed(1) || "5.0"}
              </Text>
              <Text style={styles.sellerStat}>
                {sellerStats?.totalSales?.toLocaleString() || "0"} Sold
              </Text>
              <Text style={styles.sellerStat}>
                {userReviews?.length || 0} Reviews
              </Text>
            </View>
          </View>
          <TouchableOpacity style={styles.messageButton}>
            <Ionicons name="chatbubble-outline" size={20} color="#1a96d2" />
          </TouchableOpacity>
        </View>
      </View>
      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TabView
          navigationState={{ index: tabIndex, routes }}
          renderScene={SceneMap({
            details: renderDetailsTab,
            seller: renderSellerTab,
          })}
          onIndexChange={setTabIndex}
          initialLayout={{ width: width - 32 }}
          renderTabBar={renderTabBar}
          style={styles.tabView}
        />
      </View>
      {/* Action Buttons */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.offerButton}>
          <Text style={styles.offerButtonText}>Make Offer</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.buyButton}>
          <Text style={styles.buyButtonText}>Buy Now</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#18181A",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#18181A",
  },
  emptyText: {
    color: "#fff",
    fontSize: 18,
    marginTop: 16,
  },
  imageContainer: {
    width: "100%",
    height: width * 0.7,
    backgroundColor: "#232325",
    justifyContent: "center",
    alignItems: "center",
    position: "relative",
  },
  image: {
    width: width,
    height: width * 0.7,
    backgroundColor: "#232325",
  },
  noImage: {
    justifyContent: "center",
    alignItems: "center",
  },
  closeButton: {
    position: "absolute",
    top: 60,
    right: 16,
    backgroundColor: "rgba(0,0,0,0.5)",
    borderRadius: 20,
    padding: 4,
    zIndex: 10,
  },
  headerCard: {
    backgroundColor: "#232325",
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    padding: 20,
    marginBottom: 8,
    alignItems: "center",
  },
  title: {
    color: "#fff",
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 4,
    textAlign: "center",
  },
  available: {
    color: "#1a96d2",
    fontSize: 15,
    marginBottom: 4,
  },
  price: {
    color: "#fff",
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 4,
  },
  shipping: {
    color: "#aaa",
    fontSize: 14,
    fontWeight: "400",
  },
  actionRow: {
    flexDirection: "row",
    gap: 24,
    marginTop: 12,
    justifyContent: "center",
  },
  iconButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    backgroundColor: "#29292b",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  iconLabel: {
    color: "#fff",
    fontSize: 14,
    marginLeft: 4,
  },
  sellerCard: {
    backgroundColor: "#232325",
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 12,
    marginBottom: 8,
    marginTop: 8,
  },
  sellerRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  sellerAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 8,
  },
  sellerAvatarFallback: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "#444",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  sellerName: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "bold",
  },
  sellerStatsRow: {
    flexDirection: "row",
    gap: 16,
    marginTop: 2,
  },
  sellerStat: {
    color: "#1a96d2",
    fontSize: 14,
    fontWeight: "600",
  },
  messageButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#18181A",
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 8,
    marginLeft: 8,
    // borderWidth: 1,
    // borderColor: '#1a96d2',
  },
  messageButtonText: {
    color: "#1a96d2",
    fontWeight: "bold",
    fontSize: 16,
    marginLeft: 6,
  },
  reviewsSection: {
    backgroundColor: "#232325",
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 12,
    marginBottom: 8,
    marginTop: 8,
  },
  reviewCard: {
    backgroundColor: "#18181A",
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
  },
  reviewHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 4,
  },
  reviewerAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 4,
  },
  reviewerAvatarFallback: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#444",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 4,
  },
  reviewerName: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 14,
    marginRight: 8,
  },
  reviewRating: {
    color: "#1a96d2",
    fontWeight: "bold",
    fontSize: 14,
    marginLeft: "auto",
  },
  reviewText: {
    color: "#fff",
    fontSize: 15,
  },
  tabsContainer: {
    marginHorizontal: 12,
    marginTop: 8,
    backgroundColor: "#232325",
    borderRadius: 16,
    overflow: "hidden",
  },
  tabView: {
    backgroundColor: "#232325",
    borderRadius: 16,
  },
  tabContent: {
    padding: 16,
  },
  sectionLabel: {
    color: "#aaa",
    fontSize: 15,
    fontWeight: "bold",
    marginBottom: 2,
  },
  sectionValue: {
    color: "#fff",
    fontSize: 16,
    marginBottom: 8,
  },
  sectionRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  bottomActions: {
    flexDirection: "row",
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 24,
    paddingHorizontal: 12,
    gap: 8,
  },
  offerButton: {
    backgroundColor: "#333",
    paddingHorizontal: 28,
    paddingVertical: 14,
    borderRadius: 12,
    width: "50%",
  },
  offerButtonText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
    textAlign: "center",
  },
  buyButton: {
    backgroundColor: "#1a96d2",
    paddingHorizontal: 28,
    paddingVertical: 14,
    borderRadius: 12,
    width: "50%",
  },
  buyButtonText: {
    color: "#000",
    fontWeight: "bold",
    fontSize: 16,
    textAlign: "center",
  },
});
