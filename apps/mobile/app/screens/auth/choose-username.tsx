import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { useMutation, useAction } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { router } from "expo-router";
import { validateUsername } from "@workspace/lib/profanity";
import { useUser } from "../../../hooks/useUser";

export default function ChooseUsernameScreen() {
  const [username, setUsername] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSellerInterested, setIsSellerInterested] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);

  const updateUser = useMutation(api.users.update);
  const generateUsername = useAction(api.users.generateUsername);
  const { user } = useUser();

  useEffect(() => {
    // Generate initial suggestions
    generateUsername({ name: "user" })
      .then(setSuggestions)
      .catch(console.error);
  }, []);

  const handleSubmit = async () => {
    if (!username) {
      setError("Username is required");
      return;
    }

    if (!user) {
      setError("User not found");
      return;
    }

    // Validate username
    const validation = validateUsername(username);
    if (!validation.isValid) {
      setError(validation.error || "Invalid username");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await updateUser({
        id: user._id,
        username,
        isSellerInterested,
        finishedSignUp: true,
      });

      router.push("/screens/auth/choose-categories");
    } catch (err: any) {
      setError(err.message || "Failed to update username. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Add real-time validation as user types
  const handleUsernameChange = (text: string) => {
    setUsername(text);
    if (text) {
      const validation = validateUsername(text);
      if (!validation.isValid) {
        setError(validation.error || "Invalid username");
      } else {
        setError(null);
      }
    } else {
      setError(null);
    }
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={styles.content}>
            <View style={styles.topContent}>
              <Text style={styles.title}>Set Up Your Profile</Text>
              <Text style={[styles.title, styles.subtitle2]}>
                Choose your username
              </Text>
              <Text style={styles.subtitle}>
                You can always change this later.
              </Text>

              <View style={styles.form}>
                <View style={styles.formContainer}>
                  <View>
                    <TextInput
                      style={styles.input}
                      placeholder="Enter username"
                      placeholderTextColor="#6B7280"
                      value={username}
                      onChangeText={handleUsernameChange}
                      autoCapitalize="none"
                      autoCorrect={false}
                      onSubmitEditing={dismissKeyboard}
                    />

                    <View style={styles.suggestionsContainer}>
                      {suggestions.map((suggestion, index) => (
                        <TouchableOpacity
                          key={index}
                          style={[
                            styles.suggestionButton,
                            username === suggestion &&
                              styles.selectedSuggestion,
                          ]}
                          onPress={() => setUsername(suggestion)}
                        >
                          <Text
                            style={[
                              styles.suggestionText,
                              username === suggestion &&
                                styles.selectedSuggestionText,
                            ]}
                          >
                            {suggestion}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>

                    {/* TODO: Onboarding "I'm interested in selling on Liveciety!" */}
                    {/* <View style={styles.checkboxContainer}>
                      <Checkbox
                        id="seller-interest"
                        checked={isSellerInterested}
                        onCheckedChange={setIsSellerInterested}
                      />
                      <Text style={styles.checkboxLabel}>
                        I'm interested in selling on Liveciety!
                      </Text>
                    </View> */}
                  </View>
                </View>
                {error && <Text style={styles.errorText}>{error}</Text>}
              </View>
            </View>

            <View style={styles.bottomContent}>
              <TouchableOpacity
                style={styles.button}
                onPress={handleSubmit}
                disabled={isLoading}
              >
                <Text style={styles.buttonText}>
                  {isLoading ? "Saving..." : "Next"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "transparent",
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: "space-between",
  },
  topContent: {
    paddingHorizontal: 20,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    alignItems: "center",
  },
  bottomContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    width: "100%",
  },
  formContainer: {
    width: "100%",
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#fff",
    textAlign: "center",
    marginBottom: 12,
  },
  subtitle2: {
    fontSize: 24,
    marginTop: 20,
  },
  subtitle: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    marginBottom: 32,
  },
  form: {
    width: "100%",
    maxWidth: 400,
  },
  button: {
    height: 40,
    width: "100%",
    backgroundColor: "#fff",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  buttonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "600",
  },
  errorText: {
    color: "#EF4444",
    fontSize: 12,
    marginTop: 8,
    marginBottom: 16,
  },
  input: {
    height: 40,
    backgroundColor: "#1A1A1A",
    borderWidth: 1,
    borderColor: "#333",
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    color: "#fff",
    width: "100%",
    marginBottom: 16,
  },
  suggestionsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
    marginBottom: 24,
  },
  suggestionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: "#1A1A1A",
    borderWidth: 1,
    borderColor: "#333",
  },
  selectedSuggestion: {
    backgroundColor: "#fff",
  },
  suggestionText: {
    color: "#fff",
    fontSize: 14,
  },
  selectedSuggestionText: {
    color: "#000",
  },
  checkboxContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 24,
  },
  checkboxLabel: {
    color: "#fff",
    marginLeft: 8,
    fontSize: 14,
  },
});
