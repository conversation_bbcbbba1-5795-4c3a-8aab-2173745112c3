import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
  ScrollView,
} from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import {
  categories,
  subcategories,
  Subcategory,
  Category,
} from "@workspace/lib/constants/categories";
import { Ionicons } from "@expo/vector-icons";

export default function ChooseSubcategoriesScreen() {
  const { preselectedSubcategories } = useLocalSearchParams<{
    preselectedSubcategories?: string;
  }>();

  const [selectedSubcategories, setSelectedSubcategories] = useState<
    Set<string>
  >(() => {
    if (preselectedSubcategories) {
      try {
        return new Set(JSON.parse(preselectedSubcategories));
      } catch (e) {
        return new Set();
      }
    }
    return new Set();
  });
  const [isLoading, setIsLoading] = useState(false);
  const updateUser = useMutation(api.users.update);
  const user = useQuery(api.users.viewer);

  // Get categories from user preferences
  const categoryIds = user?.preferences?.categories || [];
  const selectedCategories = categoryIds
    .map((id: string) => categories.find((c) => c.id === id))
    .filter(Boolean);

  // Get subcategories grouped by category
  const groupedSubcategories = categoryIds.reduce(
    (
      acc: Record<string, { category: Category; subcats: Subcategory[] }>,
      id: string,
    ) => {
      const category = categories.find((c) => c.id === id);
      if (category) {
        acc[id] = {
          category,
          subcats: subcategories[id] || [],
        };
      }
      return acc;
    },
    {},
  );

  const toggleSubcategory = (subcategoryId: string) => {
    setSelectedSubcategories((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(subcategoryId)) {
        newSet.delete(subcategoryId);
      } else {
        newSet.add(subcategoryId);
      }
      return newSet;
    });
  };

  const handleBack = () => {
    router.back();
  };

  const handleContinue = async () => {
    if (selectedSubcategories.size === 0) return;

    setIsLoading(true);
    try {
      await updateUser({
        id: user!._id,
        preferences: {
          ...user?.preferences,
          subcategories: Array.from(selectedSubcategories),
        },
      });

      router.replace("/");
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.content}>
          <Text style={styles.title}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={styles.content}>
            <View style={styles.header}>
              <View style={styles.titleContainer}>
                <TouchableOpacity
                  onPress={handleBack}
                  style={styles.backButton}
                >
                  <Ionicons
                    name="chevron-back-outline"
                    size={24}
                    color="#fff"
                  />
                </TouchableOpacity>
                <Text style={styles.title}>Build your ideal experience</Text>
              </View>
              <Text style={styles.subtitle}>
                Pick the specific items you'd like to see in your categories
              </Text>
            </View>

            <ScrollView
              style={styles.scrollView}
              contentContainerStyle={styles.scrollContent}
              showsVerticalScrollIndicator={false}
            >
              {Object.entries(groupedSubcategories).map(
                ([categoryId, { category, subcats }]) => (
                  <View key={categoryId} style={styles.categorySection}>
                    <Text style={styles.categoryTitle}>{category.title}</Text>
                    <View style={styles.tagsContainer}>
                      {subcats.map((item: Subcategory) => (
                        <TouchableOpacity
                          key={item.id}
                          style={[
                            styles.tag,
                            selectedSubcategories.has(item.id) &&
                              styles.selectedTag,
                          ]}
                          onPress={() => toggleSubcategory(item.id)}
                        >
                          <Text
                            style={[
                              styles.tagText,
                              selectedSubcategories.has(item.id) &&
                                styles.selectedTagText,
                            ]}
                          >
                            {item.title}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>
                ),
              )}
            </ScrollView>

            <View style={styles.bottomContent}>
              <TouchableOpacity
                style={[
                  styles.button,
                  selectedSubcategories.size === 0 && styles.buttonDisabled,
                ]}
                onPress={handleContinue}
                disabled={selectedSubcategories.size === 0 || isLoading}
              >
                <Text style={styles.buttonText}>
                  {isLoading ? "Saving..." : "Continue"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "transparent",
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: Platform.OS === "ios" ? 20 : 40,
    backgroundColor: "transparent",
  },
  titleContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
    marginBottom: 12,
    position: "relative",
  },
  backButton: {
    position: "absolute",
    left: 0,
    padding: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#fff",
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    marginBottom: 24,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  categorySection: {
    marginBottom: 24,
  },
  categoryTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 12,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: "#1A1A1A",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#333",
  },
  selectedTag: {
    borderColor: "#1a96d2",
    backgroundColor: "#2A2A2A",
  },
  tagText: {
    color: "#fff",
    fontSize: 14,
  },
  selectedTagText: {
    color: "#fff",
    fontWeight: "600",
  },
  bottomContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "#333",
    backgroundColor: "transparent",
  },
  button: {
    height: 48,
    width: "100%",
    backgroundColor: "#fff",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  buttonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "600",
  },
});
