import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { useAction, useMutation, useQuery } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { router } from "expo-router";
// import CountryPicker, { Country, CountryCode, DARK_THEME } from "react-native-country-picker-modal";

export default function CompleteSignupScreen() {
  const [phone, setPhone] = useState("");
  const [formattedPhone, setFormattedPhone] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [verificationSent, setVerificationSent] = useState(false);
  const [verificationCode, setVerificationCode] = useState("");
  // const [countryCode, setCountryCode] = useState<CountryCode>("US");
  const [callingCode, setCallingCode] = useState("1");
  const [countryPickerVisible, setCountryPickerVisible] = useState(false);
  const hasActiveCode = useQuery(api.users.hasActiveVerificationCode);
  const viewer = useQuery(api.users.viewer);

  const updateUser = useMutation(api.users.update);
  const sendVerificationCode = useAction(api.users.sendVerificationCode);
  const verifyCode = useMutation(api.users.verifyCode);

  useEffect(() => {
    if (hasActiveCode) {
      setVerificationSent(true);
      if (viewer?.phone) {
        setFormattedPhone(viewer.phone);
      }
    }
  }, [hasActiveCode, viewer]);

  // const onSelectCountry = (country: Country) => {
  //   setCountryCode(country.cca2);
  //   setCallingCode(country.callingCode[0]);
  //   setCountryPickerVisible(false);
  // };

  const handlePhoneChange = (text: string) => {
    const cleaned = text.replace(/[^0-9]/g, "");
    setPhone(cleaned);
    setFormattedPhone(`+${callingCode}${cleaned}`);
  };

  const handleSendVerification = async () => {
    if (!phone) {
      setError("Phone number is required");
      return;
    }

    if (phone.length < 10) {
      setError("Please enter a valid phone number");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await sendVerificationCode({
        phone: formattedPhone,
      });

      await updateUser({ id: user._id, phone: formattedPhone });

      setVerificationSent(true);
    } catch (err: any) {
      setError(
        err.message || "Failed to send verification code. Please try again.",
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!verificationCode) {
      setError("Verification code is required");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await verifyCode({
        code: verificationCode,
        phone: formattedPhone,
      });

      router.replace("/screens/auth/choose-username");
    } catch (err: any) {
      setError(err.message || "Failed to verify code. Please try again.");

      if (
        err.message.includes("expired") ||
        err.message.includes("too many attempts")
      ) {
        setVerificationSent(false);
        setVerificationCode("");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={styles.content}>
            <View style={styles.topContent}>
              <Text style={styles.title}>
                {hasActiveCode
                  ? "Verify your phone number"
                  : "What's your phone number?"}
              </Text>
              <Text style={styles.subtitle}>
                {hasActiveCode
                  ? "We'll use this to verify your account and keep it secure"
                  : "We'll use this to verify your account and keep it secure"}
              </Text>

              <View style={styles.form}>
                {!verificationSent ? (
                  <View style={styles.formContainer}>
                    <View style={styles.phoneInputContainer}>
                      <TouchableOpacity
                        style={styles.countryPickerButton}
                        onPress={() => setCountryPickerVisible(true)}
                      >
                        {/* <CountryPicker
                          countryCode={countryCode}
                          withFilter
                          withFlag
                          withCountryNameButton={false}
                          onSelect={onSelectCountry}
                          onClose={() => setCountryPickerVisible(false)}
                          visible={countryPickerVisible}
                          theme={DARK_THEME}
                        /> */}
                      </TouchableOpacity>

                      <Text style={styles.callingCodeText}>+{callingCode}</Text>
                      <View style={styles.divider} />

                      <TextInput
                        style={styles.phoneInput}
                        placeholder="Phone number"
                        placeholderTextColor="#6B7280"
                        value={phone}
                        onChangeText={handlePhoneChange}
                        keyboardType="phone-pad"
                        maxLength={15}
                        onSubmitEditing={dismissKeyboard}
                      />
                    </View>
                  </View>
                ) : (
                  <View>
                    <TextInput
                      style={styles.input}
                      placeholder="Enter verification code"
                      placeholderTextColor="#6B7280"
                      value={verificationCode}
                      onChangeText={setVerificationCode}
                      keyboardType="number-pad"
                      onSubmitEditing={dismissKeyboard}
                    />
                  </View>
                )}
                {error && <Text style={styles.errorText}>{error}</Text>}
              </View>
            </View>

            <View style={styles.bottomContent}>
              <TouchableOpacity
                style={styles.button}
                onPress={
                  !verificationSent ? handleSendVerification : handleVerifyCode
                }
                disabled={isLoading}
              >
                <Text style={styles.buttonText}>
                  {!verificationSent
                    ? isLoading
                      ? "Sending..."
                      : "Continue"
                    : isLoading
                      ? "Verifying..."
                      : "Verify & Continue"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "transparent",
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: "space-between",
  },
  topContent: {
    paddingHorizontal: 20,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    alignItems: "center",
  },
  bottomContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    width: "100%",
  },
  formContainer: {
    width: "100%",
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#fff",
    textAlign: "center",
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    marginBottom: 32,
  },
  form: {
    width: "100%",
    maxWidth: 400,
  },
  phoneInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#1A1A1A",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#333",
    marginBottom: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
    position: "relative",
    height: 40,
  },
  countryPickerButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingRight: 8,
    height: 40,
  },
  callingCodeText: {
    color: "#fff",
    fontSize: 16,
    paddingRight: 8,
  },
  divider: {
    width: 1,
    height: 24,
    backgroundColor: "#333",
    marginRight: 12,
  },
  phoneInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: "#fff",
    padding: 0,
  },
  button: {
    height: 40,
    width: "100%",
    backgroundColor: "#fff",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  buttonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "600",
  },
  errorText: {
    color: "#EF4444",
    fontSize: 12,
    marginTop: 8,
    marginBottom: 16,
  },
  input: {
    height: 40,
    backgroundColor: "#1A1A1A",
    borderWidth: 1,
    borderColor: "#333",
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    color: "#fff",
    width: "100%",
    marginBottom: 16,
  },
  keyboardDismiss: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  keyboardDismissText: {
    color: "#3B82F6",
    fontSize: 14,
    fontWeight: "500",
  },
});
