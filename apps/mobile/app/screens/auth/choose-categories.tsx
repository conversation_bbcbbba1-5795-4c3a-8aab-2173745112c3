import React, { useState } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  StyleSheet,
  SafeAreaView,
  Platform,
  KeyboardAvoidingView,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { router } from "expo-router";
import { useMutation } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { categories, Category } from "../../../../../packages/lib/constants/categories";
import { useUser } from "../../../hooks/useUser";

export default function ChooseCategoriesScreen() {
  const [selectedCategories, setSelectedCategories] = useState<Set<string>>(
    new Set(),
  );
  const [isLoading, setIsLoading] = useState(false);
  const updateUser = useMutation(api.users.update);
  const { user } = useUser();

  const toggleCategory = (categoryId: string) => {
    setSelectedCategories((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  const handleContinue = async () => {
    if (selectedCategories.size === 0 || !user) return;

    setIsLoading(true);
    try {
      await updateUser({
        id: user._id,
        preferences: {
          categories: Array.from(selectedCategories),
        },
      });

      // Always navigate to subcategories selection
      const selectedCategoriesArray = Array.from(selectedCategories);
      router.push({
        pathname: "/screens/auth/choose-subcategories",
        params: {
          categoryId: selectedCategoriesArray.join(","), // Pass all selected categories
          preselectedSubcategories: "[]", // Initialize with empty array
        },
      });
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const renderItem = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={[
        styles.categoryCard,
        selectedCategories.has(item.id) && styles.selectedCard,
      ]}
      onPress={() => toggleCategory(item.id)}
    >
      <Image source={item.image} style={styles.categoryImage} />
      <View style={styles.categoryContent}>
        <Text style={styles.categoryTitle}>{item.title}</Text>
        <Text style={styles.categoryDescription}>{item.description}</Text>
      </View>
    </TouchableOpacity>
  );

  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <View style={styles.content}>
            <View style={styles.topContent}>
              <Text style={styles.title}>What are you interested in?</Text>
              <Text style={styles.subtitle}>
                Select the categories that interest you
              </Text>

              <View style={styles.form}>
                <FlatList
                  data={categories}
                  renderItem={renderItem}
                  keyExtractor={(item) => item.id}
                  contentContainerStyle={styles.list}
                  showsVerticalScrollIndicator={false}
                />
              </View>
            </View>

            <View style={styles.bottomContent}>
              <TouchableOpacity
                style={[
                  styles.button,
                  selectedCategories.size === 0 && styles.buttonDisabled,
                ]}
                onPress={handleContinue}
                disabled={selectedCategories.size === 0 || isLoading}
              >
                <Text style={styles.buttonText}>
                  {isLoading ? "Saving..." : "Continue"}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "transparent",
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: "space-between",
  },
  topContent: {
    paddingHorizontal: 20,
    paddingTop: Platform.OS === "ios" ? 60 : 40,
    alignItems: "center",
    flex: 1,
  },
  bottomContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    width: "100%",
  },
  form: {
    width: "100%",
    maxWidth: 400,
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#fff",
    textAlign: "center",
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: "#6B7280",
    textAlign: "center",
    marginBottom: 32,
  },
  list: {
    width: "100%",
  },
  categoryCard: {
    flexDirection: "row",
    backgroundColor: "#1A1A1A",
    borderRadius: 8,
    marginBottom: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: "#333",
  },
  selectedCard: {
    borderColor: "#1a96d2",
    backgroundColor: "#2A2A2A",
  },
  categoryImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  categoryContent: {
    flex: 1,
    marginLeft: 12,
    justifyContent: "center",
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: "#6B7280",
  },
  button: {
    height: 40,
    width: "100%",
    backgroundColor: "#fff",
    borderRadius: 8,
    alignItems: "center",
    justifyContent: "center",
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  buttonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "600",
  },
});
