import React from "react";
import { Stack, useLocalSearchParams } from "expo-router";
import { ChatScreen } from "../../components/ui/ChatScreen";
import { useRouter } from "expo-router";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";

export default function ChatRoute() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const deleteChat = useMutation(api.chat.deleteChat);
  const messages = useQuery(api.chat.getMessages, {
    chatId: id as Id<"chats">,
  });

  const handleBack = async () => {
    // If there are no messages, delete the chat before navigating back
    if (messages && messages.length === 0) {
      try {
        await deleteChat({ chatId: id as Id<"chats"> });
      } catch (error) {
        console.error("Failed to delete empty chat:", error);
      }
    }

    // Navigate back to the activity tab with messages selected
    router.replace("/(tabs)/activity?tab=messages");
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />
      <ChatScreen chatId={id as Id<"chats">} onBack={handleBack} />
    </>
  );
}
