import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import LiveStreamCard from "./LiveStreamCard";
import { Doc, Id } from "../convex/_generated/dataModel";
import { categories } from "@workspace/lib/constants";
import { router } from "expo-router";

type Stream = Doc<"streams"> & {
  username?: string;
  profileImageUrl?: string;
  userColor?: string;
  viewerCount?: number;
  isBookmarked?: boolean;
};

interface FollowedHostsProps {
  userId: Id<"users">;
}

export default function FollowedHosts({ userId }: FollowedHostsProps) {
  // Get followed hosts and their streams
  const followedHostsResult = useQuery(api.users.getFollowing, {
    userId,
    paginationOpts: {
      numItems: 20,
      cursor: null
    }
  });
  const activeStreams = useQuery(api.streams.listActiveStreams, { category: undefined }) as
    | Stream[]
    | undefined;

  // Extract followed hosts
  const followedHosts = followedHostsResult?.users || [];

  // Filter streams to only show those from followed sellers
  const followedHostsStreams = React.useMemo(() => {
    if (!activeStreams || !followedHosts) return [];

    return activeStreams.filter((stream) =>
      followedHosts.some(
        (host: { _id: Id<"users">; role: string }) => host._id === stream.hostId && host.role === "seller",
      ),
    );
  }, [activeStreams, followedHosts]);

  // Check if user follows any sellers
  const hasFollowedSellers = followedHosts?.some(
    (host: { _id: Id<"users">; role: string }) => host.role === "seller",
  );

  if (!hasFollowedSellers) {
    return null; // Don't render anything if user doesn't follow any sellers
  }

  return (
    <View style={styles.container}>
      {followedHostsStreams.length > 0 ? (
        <View style={styles.streamsGrid}>
          {followedHostsStreams.map((stream) => (
            <LiveStreamCard
              key={stream._id}
              username={stream.username || ""}
              title={stream.title}
              category={
                categories.find((cat) => cat.id === stream.category)?.title ||
                ""
              }
              subcategory={stream.subcategory || ""}
              viewerCount={stream.viewerCount || 0}
              thumbnailUrl={stream.thumbnail || ""}
              profileImageUrl={stream.profileImageUrl || ""}
              userColor={stream.userColor}
              streamId={stream._id}
              isBookmarked={stream.isBookmarked || false}
              onPress={() => {
                router.push(`/screens/stream/${stream._id}`);
              }}
            />
          ))}
        </View>
      ) : (
        <View style={styles.noStreamsContainer}>
          <Text style={styles.noStreamsText}>
            No current streams from followed hosts
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  streamsGrid: {
    padding: 16,
  },
  noStreamsContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 32,
  },
  noStreamsText: {
    color: "#8E8E93",
    fontSize: 16,
    fontWeight: "500",
  },
});
