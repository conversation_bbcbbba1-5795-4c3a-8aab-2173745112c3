import React, { useState, useEffect } from "react";
import { View, Text, Image, StyleSheet, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import UserAvatar from "./ui/user-avatar";
import { getImageUrl } from "../lib/utils";
import { Link } from 'expo-router';

interface LiveStreamCardProps {
  username: string;
  title: string;
  category: string;
  subcategory: string;
  viewerCount: number;
  thumbnailUrl: string;
  profileImageUrl: string | null | undefined;
  streamId: Id<"streams">;
  isBookmarked: boolean;
  userColor?: string;
  onPress?: () => void;
}

export default function LiveStreamCard({
  username,
  title,
  category,
  subcategory,
  viewerCount,
  thumbnailUrl,
  profileImageUrl,
  streamId,
  isBookmarked: initialIsBookmarked,
  userColor = "#2C2C2E",
  onPress,
}: LiveStreamCardProps) {
  const toggleBookmark = useMutation(api.bookmarks.toggleBookmark);
  const [isBookmarked, setIsBookmarked] = useState(initialIsBookmarked);

  // Process the URLs to ensure they're valid
  const processedThumbnailUrl = getImageUrl(thumbnailUrl || "");

  // Update local state when prop changes
  useEffect(() => {
    setIsBookmarked(initialIsBookmarked);
  }, [initialIsBookmarked]);

  const handleBookmarkPress = async (e: any) => {
    e.stopPropagation();
    try {
      // Optimistically update the UI
      setIsBookmarked(!isBookmarked);

      // Perform the mutation
      const result = await toggleBookmark({ streamId });

      // If the mutation fails or returns a different state, revert
      if (result.bookmarked !== !isBookmarked) {
        setIsBookmarked(result.bookmarked);
      }
    } catch (error) {
      // Revert on error
      setIsBookmarked(isBookmarked);
      console.error("Error toggling bookmark:", error);
    }
  };

  return (
    <Link href={`/screens/stream/${streamId}`} asChild>
      <TouchableOpacity style={styles.container} onPress={onPress}>
        <View style={styles.thumbnailContainer}>
          <Image
            source={{ uri: processedThumbnailUrl }}
            style={styles.thumbnail}
          />
          <View style={styles.liveContainer}>
            <View style={styles.liveIndicator}>
              <View style={styles.liveDot} />
              <Text style={styles.liveText}>
                {viewerCount > 0 ? viewerCount : "0"}
              </Text>
            </View>
          </View>
          <TouchableOpacity
            style={styles.bookmarkButton}
            onPress={handleBookmarkPress}
          >
            <Ionicons
              name={isBookmarked ? "bookmark" : "bookmark-outline"}
              size={24}
              color="#fff"
            />
          </TouchableOpacity>
        </View>

        <View style={styles.infoContainer}>
          <UserAvatar
            image={getImageUrl(profileImageUrl || "")}
            username={username || ""}
            color={userColor}
            size={40}
            style={styles.profileImage}
          />
          <View style={styles.textContainer}>
            <Text style={styles.username}>{username}</Text>
            <Text style={styles.title} numberOfLines={2}>
              {title}
            </Text>
            <Text style={styles.category}>
              {category} • {subcategory}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    </Link>
  );
}

const styles = StyleSheet.create({
  container: {
    width: "100%",
    marginBottom: 16,
    backgroundColor: "#1C1C1E",
    borderRadius: 16,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#2C2C2E",
  },
  thumbnailContainer: {
    position: "relative",
    aspectRatio: 16 / 9,
  },
  thumbnail: {
    width: "100%",
    height: "100%",
    backgroundColor: "#2C2C2E",
  },
  liveContainer: {
    position: "absolute",
    top: 8,
    left: 8,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  bookmarkButton: {
    position: "absolute",
    top: 8,
    right: 8,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    padding: 8,
    borderRadius: 8,
  },
  liveIndicator: {
    flexDirection: "row",
    alignItems: "center",
  },
  liveDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: "#FF3B30",
    marginRight: 6,
  },
  liveText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "500",
  },
  infoContainer: {
    flexDirection: "row",
    padding: 12,
  },
  profileImage: {
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  username: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 4,
  },
  title: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  category: {
    color: "#8E8E93",
    fontSize: 12,
  },
});
