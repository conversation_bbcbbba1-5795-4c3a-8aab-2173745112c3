import React from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ImageBackground,
} from "react-native";
import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import LiveStreamCard from "./LiveStreamCard";
import { Doc } from "../convex/_generated/dataModel";
import { categories, subcategories } from "../../../packages/lib/constants/categories";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";

type Stream = Doc<"streams"> & {
  username?: string;
  profileImageUrl?: string;
  userColor?: string;
  viewerCount?: number;
  isBookmarked?: boolean;
};

interface CategoryStreamsProps {
  selectedCategory: string;
}

interface SubcategoryWithStats {
  id: string;
  title: string;
  viewers: number;
  image: any;
}

export default function CategoryStreams({
  selectedCategory,
}: CategoryStreamsProps) {
  const [selectedFilter, setSelectedFilter] = React.useState("Recommended");
  const [selectedSubcategory, setSelectedSubcategory] = React.useState<
    string | null
  >(null);
  const activeStreams = useQuery(api.streams.listActiveStreams, { category: undefined }) as
    | Stream[]
    | undefined;

  // Get the category ID from the selected category title
  const categoryId = categories.find(
    (cat) => cat.title === selectedCategory,
  )?.id;

  // Get subcategories for the selected category and calculate viewers
  const subcategoriesWithStats = React.useMemo(() => {
    if (!categoryId || !activeStreams) return [];

    const categorySubcats = subcategories[categoryId] || [];
    const mainCategoryImage = categories.find(
      (cat) => cat.id === categoryId,
    )?.image;

    return categorySubcats
      .map((subcat) => {
        // Calculate total viewers for this subcategory
        const subcatViewers = activeStreams
          .filter((stream) => stream.subcategory === subcat.id)
          .reduce((total, stream) => total + (stream.viewerCount || 0), 0);

        return {
          ...subcat,
          viewers: subcatViewers,
          image: mainCategoryImage, // Using main category image for now
        };
      })
      .sort((a, b) => b.viewers - a.viewers); // Sort by viewers count
  }, [categoryId, activeStreams]);

  // Filter streams for the selected category and subcategory
  const categoryStreams = React.useMemo(() => {
    if (!activeStreams || !categoryId) return [];

    let filtered = activeStreams.filter(
      (stream) => stream.category === categoryId,
    );

    if (selectedSubcategory) {
      filtered = filtered.filter(
        (stream) => stream.subcategory === selectedSubcategory,
      );
    }

    return filtered;
  }, [activeStreams, categoryId, selectedSubcategory]);

  const filters = ["Recommended", "Top Sellers", "New and Notable"];

  return (
    <View style={styles.container}>
      {/* Subcategory Cards */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.categoryCardsContent}
      >
        {subcategoriesWithStats.map((subcat) => (
          <TouchableOpacity
            key={subcat.id}
            style={[
              styles.categoryCard,
              selectedSubcategory === subcat.id && styles.categoryCardSelected,
            ]}
            onPress={() =>
              setSelectedSubcategory(
                selectedSubcategory === subcat.id ? null : subcat.id,
              )
            }
          >
            <ImageBackground
              source={subcat.image}
              style={styles.categoryCardBackground}
              imageStyle={styles.categoryCardImage}
            >
              <Text style={styles.categoryCardTitle}>{subcat.title}</Text>
              {subcat.viewers > 0 && (
                <View style={styles.viewersContainer}>
                  <Ionicons name="people" size={16} color="#fff" />
                  <Text style={styles.viewersText}>0 Viewers</Text>
                </View>
              )}
            </ImageBackground>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Filters */}
      {/* TODO: Add filters */}
      {/* <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filtersContainer}
        contentContainerStyle={styles.filtersContent}
      >
        <TouchableOpacity style={styles.filterButton}>
          <Ionicons name="filter" size={18} color="#fff" />
          <Text style={styles.filterText}>Filter</Text>
        </TouchableOpacity>
        {filters.map((filter, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.filterChip,
              selectedFilter === filter && styles.filterChipSelected
            ]}
            onPress={() => setSelectedFilter(filter)}
          >
            <Text style={[
              styles.filterChipText,
              selectedFilter === filter && styles.filterChipTextSelected
            ]}>
              {filter}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView> */}

      {/* Streams Grid */}
      <View style={styles.streamsGrid}>
        {categoryStreams.length > 0 ? (
          categoryStreams.map((stream) => (
            <LiveStreamCard
              key={stream._id}
              username={stream.username || ""}
              title={stream.title}
              category={
                categories.find((cat) => cat.id === stream.category)?.title ||
                ""
              }
              subcategory={(() => {
                if (!stream.subcategory) return "";
                const subcatArr = subcategories[String(stream.category)] || [];
                return (
                  subcatArr.find(
                    (s: { id: string; title: string }) =>
                      s.id === stream.subcategory,
                  )?.title || stream.subcategory
                );
              })()}
              viewerCount={stream.viewerCount || 0}
              thumbnailUrl={stream.thumbnail || ""}
              profileImageUrl={stream.profileImageUrl || ""}
              userColor={stream.userColor}
              streamId={stream._id}
              isBookmarked={stream.isBookmarked || false}
              onPress={() => {
                router.push(`/screens/stream/${stream._id}`);
              }}
            />
          ))
        ) : (
          <View style={styles.noStreamsContainer}>
            <Text style={styles.noStreamsText}>
              {selectedSubcategory
                ? "No current streams in this subcategory"
                : "No current streams in this category"}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  categoryCardsContent: {},
  categoryCard: {
    width: 160,
    height: 90,
    borderRadius: 12,
    overflow: "hidden",
    marginRight: 12,
  },
  categoryCardSelected: {
    borderWidth: 1,
    borderColor: "#fff",
  },
  categoryCardBackground: {
    flex: 1,
    justifyContent: "space-between",
    padding: 12,
  },
  categoryCardImage: {
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#555",
    opacity: 0.4,
  },
  categoryCardTitle: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
    textShadowColor: "rgba(0, 0, 0, 0.75)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 4,
  },
  viewersContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: "flex-start",
  },
  viewersText: {
    color: "#fff",
    fontSize: 12,
    marginLeft: 4,
  },
  filtersContainer: {
    marginTop: 16,
  },
  filtersContent: {
    paddingHorizontal: 16,
    gap: 8,
    flexDirection: "row",
    alignItems: "center",
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#2C2C2E",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  filterText: {
    color: "#fff",
    fontSize: 14,
    marginLeft: 4,
  },
  filterChip: {
    backgroundColor: "#2C2C2E",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  filterChipSelected: {
    backgroundColor: "#fff",
  },
  filterChipText: {
    color: "#fff",
    fontSize: 14,
  },
  filterChipTextSelected: {
    color: "#000",
  },
  streamsGrid: {
    padding: 16,
  },
  noStreamsContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 32,
  },
  noStreamsText: {
    color: "#8E8E93",
    fontSize: 16,
    fontWeight: "500",
  },
});
