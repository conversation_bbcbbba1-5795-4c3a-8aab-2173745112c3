import React, { useState } from "react";
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { Ionicons } from "@expo/vector-icons";

interface ReportModalProps {
  isVisible: boolean;
  onClose: () => void;
  contentType: "message" | "user" | "profile";
  contentId: Id<"messages"> | Id<"users"> | string;
  reportedUserId: Id<"users">;
}

const REPORT_REASONS = [
  "Harassment or Bullying",
  "Inappropriate Content",
  "Spam",
  "Hate Speech",
  "Violence",
  "Other",
];

export const ReportModal: React.FC<ReportModalProps> = ({
  isVisible,
  onClose,
  contentType,
  contentId,
  reportedUserId,
}) => {
  const [selectedReason, setSelectedReason] = useState<string>("");
  const [additionalDetails, setAdditionalDetails] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const submitReport = useMutation(api.moderation.submitReport);
  const blockUser = useMutation(api.moderation.blockUser);

  const handleSubmitReport = async () => {
    if (!selectedReason) {
      Alert.alert("Error", "Please select a reason for reporting");
      return;
    }

    setIsSubmitting(true);
    try {
      await submitReport({
        contentType,
        contentId: contentId.toString(),
        reportedUserId,
        reason: selectedReason,
        additionalDetails,
      });

      Alert.alert(
        "Report Submitted",
        "Thank you for helping keep our community safe. We will review this report within 24 hours.",
        [{ text: "OK", onPress: onClose }],
      );
    } catch (error) {
      Alert.alert("Error", "Failed to submit report. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBlockUser = async () => {
    Alert.alert(
      "Block User",
      "Are you sure you want to block this user? You won't see their content anymore.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Block",
          style: "destructive",
          onPress: async () => {
            try {
              await blockUser({ userId: reportedUserId });
              Alert.alert("Success", "User has been blocked");
              onClose();
            } catch (error) {
              Alert.alert("Error", "Failed to block user. Please try again.");
            }
          },
        },
      ],
    );
  };

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>Report {contentType}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content}>
            <Text style={styles.sectionTitle}>
              Why are you reporting this {contentType}?
            </Text>

            {REPORT_REASONS.map((reason) => (
              <TouchableOpacity
                key={reason}
                style={[
                  styles.reasonButton,
                  selectedReason === reason && styles.selectedReason,
                ]}
                onPress={() => setSelectedReason(reason)}
              >
                <Text
                  style={[
                    styles.reasonText,
                    selectedReason === reason && styles.selectedReasonText,
                  ]}
                >
                  {reason}
                </Text>
              </TouchableOpacity>
            ))}

            <Text style={[styles.sectionTitle, { marginTop: 20 }]}>
              Additional Details (Optional)
            </Text>
            <TextInput
              style={styles.input}
              multiline
              numberOfLines={4}
              value={additionalDetails}
              onChangeText={setAdditionalDetails}
              placeholder="Please provide any additional context..."
              placeholderTextColor="#71717A"
            />

            <TouchableOpacity
              style={styles.blockButton}
              onPress={handleBlockUser}
            >
              <Ionicons name="ban" size={20} color="#FF3B30" />
              <Text style={styles.blockButtonText}>Block User</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.submitButton,
                !selectedReason && styles.submitButtonDisabled,
              ]}
              onPress={handleSubmitReport}
              disabled={!selectedReason || isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.submitButtonText}>Submit Report</Text>
              )}
            </TouchableOpacity>
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "#1C1C1E",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: "80%",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#27272A",
  },
  title: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
  },
  closeButton: {
    padding: 4,
  },
  content: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 12,
  },
  reasonButton: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: "#27272A",
  },
  selectedReason: {
    backgroundColor: "#0A84FF",
  },
  reasonText: {
    fontSize: 16,
    color: "#fff",
  },
  selectedReasonText: {
    fontWeight: "600",
  },
  input: {
    backgroundColor: "#27272A",
    borderRadius: 8,
    padding: 12,
    color: "#fff",
    height: 100,
    textAlignVertical: "top",
    marginBottom: 20,
  },
  blockButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 8,
    backgroundColor: "#27272A",
    marginBottom: 16,
  },
  blockButtonText: {
    color: "#FF3B30",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 8,
  },
  submitButton: {
    backgroundColor: "#0A84FF",
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
    marginBottom: 32,
  },
  submitButtonDisabled: {
    opacity: 0.5,
  },
  submitButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
