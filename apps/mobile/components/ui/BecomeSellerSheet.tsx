import React from "react";
import { View, Text, StyleSheet, Platform, Alert } from "react-native";
import { CustomBottomSheet } from "./BottomSheet";
import { Button } from "./button";
import { useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";
import { useUser } from "../../hooks/useUser";

interface BecomeSellerSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onGetStarted: () => void;
}

const BecomeSellerSheet: React.FC<BecomeSellerSheetProps> = ({
  isOpen,
  onClose,
  onGetStarted,
}) => {
  const updateUser = useMutation(api.users.update);
  const { user } = useUser();

  const handleGetStarted = async () => {
    if (!user) {
      Alert.alert("Error", "User not found. Please try again.", [
        { text: "OK" },
      ]);
      return;
    }

    try {
      await updateUser({ id: user._id, isSellerInterested: true });
      Alert.alert("Success", "Request has been sent!", [
        { text: "OK", onPress: onClose },
      ]);
    } catch (error) {
      Alert.alert("Error", "Failed to submit your request. Please try again.", [
        { text: "OK" },
      ]);
    }
  };

  return (
    <>
      {!user?.isSellerInterested ? (
        <CustomBottomSheet
          isOpen={isOpen}
          onClose={onClose}
          snapPoints={["60%"]}
          enablePanDownToClose
          style={styles.bottomSheet}
        >
          <View style={styles.container}>
            <View style={styles.content}>
              <Text style={styles.title}>Interested in Selling?</Text>

              <View style={styles.features}>
                <FeatureItem
                  icon="⚡️"
                  title="Become a live seller"
                  description="Start your own live selling channel in minutes—no technical setup required!"
                />
                <FeatureItem
                  icon="💰"
                  title="Keep more of what you earn"
                  description="With just a 6% commission, Liveciety lets you maximize your profit while we handle the rest."
                />
                <FeatureItem
                  icon="👥"
                  title="Sell to anyone, anywhere!"
                  description="Reach buyers with interactive live shows and seamless checkout."
                />
              </View>

              <View style={styles.buttonContainer}>
                <Button onPress={handleGetStarted} style={styles.button}>
                  <Text style={styles.buttonText}>
                    Request to become a seller
                  </Text>
                </Button>
              </View>
            </View>
          </View>
        </CustomBottomSheet>
      ) : (
        <CustomBottomSheet
          isOpen={isOpen}
          onClose={onClose}
          snapPoints={["20%"]}
          enablePanDownToClose
          style={styles.bottomSheet}
        >
          <View style={styles.container}>
            <View style={styles.content}>
              <Text style={styles.title}>
                Already requested to become a seller!
              </Text>
            </View>
          </View>
        </CustomBottomSheet>
      )}
    </>
  );
};

const FeatureItem = ({
  icon,
  title,
  description,
}: {
  icon: string;
  title: string;
  description: string;
}) => (
  <View style={styles.featureItem}>
    <Text style={styles.featureIcon}>{icon}</Text>
    <View style={styles.featureContent}>
      <Text style={styles.featureTitle}>{title}</Text>
      <Text style={styles.featureDescription}>{description}</Text>
    </View>
  </View>
);

const styles = StyleSheet.create({
  bottomSheet: {
    backgroundColor: "#18181B",
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  container: {
    flex: 1,
    backgroundColor: "#18181B",
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: "hidden",
  },
  content: {
    padding: 24,
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 32,
    textAlign: "center",
  },
  features: {
    gap: 12,
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 4,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#333",
    backgroundColor: "#27272A80",
    padding: 12,
  },
  featureIcon: {
    fontSize: 24,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 15,
    color: "#8E8E93",
    lineHeight: 20,
  },
  buttonContainer: {
    marginTop: 12,
    paddingHorizontal: 4,
  },
  buttonText: {
    color: "#000",
    fontSize: 17,
    fontWeight: "600",
    textAlign: "center",
  },
  button: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 12,
  },
});

export default BecomeSellerSheet;
