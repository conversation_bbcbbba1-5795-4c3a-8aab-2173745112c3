import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Animated,
  TextInput,
} from "react-native";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useRouter } from "expo-router";
import { format } from "date-fns";
import MessagesEmptyState from "./MessagesEmptyState";
import { Doc, Id } from "../../convex/_generated/dataModel";
import { useState, useRef } from "react";
import { Ionicons } from "@expo/vector-icons";
import { ComposeMessageSheet } from "./ComposeMessageSheet";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import Swipeable from "react-native-gesture-handler/Swipeable";
import ChatListSkeleton from "../skeletons/ChatListSkeleton";

interface ChatItemProps {
  item: Doc<"chats"> & {
    participants: (Doc<"users"> & {
      image?: string;
      imageUrl?: string;
      color?: string;
    })[];
    otherUser:
      | (Doc<"users"> & {
          image?: string;
          imageUrl?: string;
          name?: string;
          color?: string;
        })
      | null;
    lastMessage?: Doc<"messages">;
    unreadCount?: number;
  };
  onDelete: () => void;
}

const AvatarFallback = ({
  name,
  color,
  size = 50,
}: {
  name: string;
  color: string;
  size?: number;
}) => (
  <View
    style={[
      styles.avatarFallback,
      {
        backgroundColor: color,
        width: size,
        height: size,
        borderRadius: size / 2,
      },
    ]}
  >
    <Text style={[styles.avatarLetter, { fontSize: size * 0.4 }]}>
      {name.charAt(0).toUpperCase()}
    </Text>
  </View>
);

const ChatItem = ({ item, onDelete }: ChatItemProps) => {
  const chat = item;
  const router = useRouter();
  const swipeableRef = useRef<Swipeable>(null);

  // Safety check - ensure chat object is valid
  if (!chat || typeof chat !== 'object') {
    return null;
  }

  // Ensure all values are strings and safe to render
  const title = String(chat.isGroup ? (chat.title || "Group Chat") : (chat.otherUser?.name || "Chat"));
  const lastMessageTime = chat.lastMessageAt
    ? format(new Date(chat.lastMessageAt), "HH:mm")
    : "";
  const lastMessageText = String(chat.lastMessage?.text || "No messages yet");
  const isYesterday = chat.lastMessageAt && new Date(chat.lastMessageAt).getDate() === new Date().getDate() - 1;
  const displayTime = String(isYesterday ? "Yesterday" : lastMessageTime);

  const renderRightActions = (
    _progress: Animated.AnimatedInterpolation<number>,
    dragX: Animated.AnimatedInterpolation<number>,
  ) => {
    const scale = dragX.interpolate({
      inputRange: [-100, 0],
      outputRange: [1, 0],
      extrapolate: "clamp",
    });

    return (
      <TouchableOpacity
        style={styles.deleteAction}
        onPress={() => {
          swipeableRef.current?.close();
          onDelete();
        }}
      >
        <Animated.View style={{ transform: [{ scale }] }}>
          <Ionicons name="trash-outline" size={24} color="#fff" />
        </Animated.View>
      </TouchableOpacity>
    );
  };

  return (
    <Swipeable
      ref={swipeableRef}
      renderRightActions={renderRightActions}
      rightThreshold={40}
    >
      <TouchableOpacity
        style={styles.chatItem}
        onPress={() =>
          router.push({
            pathname: "/chat/[id]",
            params: { id: chat._id },
          })
        }
      >
        {chat.isGroup ? (
          <View style={styles.groupAvatarContainer}>
            {(
              chat.participants as Array<
                Doc<"users"> & { imageUrl?: string; color?: string }
              >
            )
              .slice(0, 4)
              .map((participant, index) =>
                participant?.imageUrl ? (
                  <Image
                    key={participant._id}
                    source={{ uri: participant.imageUrl }}
                    style={[
                      styles.groupAvatar,
                      {
                        top: index > 1 ? 10 : 0,
                        left: index % 2 ? 10 : 0,
                      },
                    ]}
                  />
                ) : (
                  <AvatarFallback
                    key={participant._id}
                    name={String(participant.name || "User")}
                    color={participant.color || "#27272A"}
                    size={25}
                  />
                ),
              )}
          </View>
        ) : chat.otherUser?.imageUrl ? (
          <Image
            source={{ uri: chat.otherUser.imageUrl }}
            style={styles.avatar}
          />
        ) : (
          <AvatarFallback
            name={String(chat.otherUser?.name || "Chat")}
            color={chat.otherUser?.color || "#27272A"}
            size={50}
          />
        )}
        <View style={styles.chatInfo}>
          <View style={styles.topLine}>
            <Text style={styles.messageTitle} numberOfLines={1}>
              {title || "Chat"}
            </Text>
            <Text style={styles.timeText}>{displayTime || ""}</Text>
          </View>
          <View style={styles.bottomLine}>
            <Text style={styles.lastMessage} numberOfLines={1}>
              {lastMessageText || "No messages yet"}
            </Text>
            {chat.unreadCount && chat.unreadCount > 0 && (
              <View style={styles.unreadBadge}>
                <Text style={styles.unreadCount}>
                  {String(chat.unreadCount)}
                </Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Swipeable>
  );
};

const ChatHeader = ({
  headerOpacity,
  searchOpacity,
  searchAnimation,
  isSearchActive,
  searchQuery,
  searchInputRef,
  setSearchQuery,
  toggleSearch,
}: {
  headerOpacity: Animated.AnimatedInterpolation<number>;
  searchOpacity: Animated.AnimatedInterpolation<number>;
  searchAnimation: Animated.AnimatedValue;
  isSearchActive: boolean;
  searchQuery: string;
  searchInputRef: React.RefObject<TextInput>;
  setSearchQuery: (query: string) => void;
  toggleSearch: () => void;
}) => (
  <View style={styles.header}>
    <HeaderTitle opacity={headerOpacity} />
    <SearchBar
      searchOpacity={searchOpacity}
      searchAnimation={searchAnimation}
      isSearchActive={isSearchActive}
      searchQuery={searchQuery}
      searchInputRef={searchInputRef}
      setSearchQuery={setSearchQuery}
    />
    <HeaderButtons
      isSearchActive={isSearchActive}
      toggleSearch={toggleSearch}
    />
  </View>
);

const HeaderTitle = ({
  opacity,
}: {
  opacity: Animated.AnimatedInterpolation<number>;
}) => (
  <Animated.View style={[styles.titleContainer, { opacity }]}>
    <Text style={styles.headerTitle}>Chats</Text>
  </Animated.View>
);

const SearchBar = ({
  searchOpacity,
  searchAnimation,
  isSearchActive,
  searchQuery,
  searchInputRef,
  setSearchQuery,
}: {
  searchOpacity: Animated.AnimatedInterpolation<number>;
  searchAnimation: Animated.AnimatedValue;
  isSearchActive: boolean;
  searchQuery: string;
  searchInputRef: React.RefObject<TextInput>;
  setSearchQuery: (query: string) => void;
}) => (
  <Animated.View
    style={[
      styles.searchContainer,
      {
        transform: [
          {
            translateX: searchAnimation.interpolate({
              inputRange: [0, 1],
              outputRange: [300, 0],
            }),
          },
        ],
        opacity: searchOpacity,
        position: "absolute",
        left: 16,
        right: 100,
        zIndex: isSearchActive ? 1 : 0,
      },
    ]}
  >
    <Ionicons
      name="search"
      size={20}
      color="#71717A"
      style={styles.searchIcon}
    />
    <TextInput
      ref={searchInputRef}
      style={styles.searchInput}
      placeholder="Search chats"
      placeholderTextColor="#71717A"
      value={searchQuery}
      onChangeText={setSearchQuery}
    />
  </Animated.View>
);

const HeaderButtons = ({
  isSearchActive,
  toggleSearch,
}: {
  isSearchActive: boolean;
  toggleSearch: () => void;
}) => (
  <View style={styles.headerButtons}>
    <TouchableOpacity style={styles.headerButton} onPress={toggleSearch}>
      <Ionicons
        name={isSearchActive ? "close" : "search"}
        size={22}
        color="#fff"
      />
    </TouchableOpacity>
  </View>
);

export default function ChatList() {
  const router = useRouter();
  const chats = useQuery(api.chat.listChats, {});
  const [isComposeOpen, setIsComposeOpen] = useState(false);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const searchAnimation = useRef(new Animated.Value(0)).current;
  const searchInputRef = useRef<TextInput>(null);
  const deleteChat = useMutation(api.chat.deleteChat);

  const handleOpenCompose = () => setIsComposeOpen(true);
  const handleCloseCompose = () => setIsComposeOpen(false);

  const toggleSearch = () => {
    if (!isSearchActive) {
      setIsSearchActive(true);
      Animated.spring(searchAnimation, {
        toValue: 1,
        useNativeDriver: true,
      }).start(() => {
        searchInputRef.current?.focus();
      });
    } else {
      Animated.spring(searchAnimation, {
        toValue: 0,
        useNativeDriver: true,
      }).start(() => {
        setIsSearchActive(false);
        setSearchQuery("");
      });
    }
  };

  const handleChatCreated = (chatId: Id<"chats">) => {
    router.push({ pathname: "/chat/[id]", params: { id: chatId } });
    setIsComposeOpen(false);
  };

  const handleDeleteChat = async (chatId: Id<"chats">) => {
    try {
      await deleteChat({ chatId });
    } catch (error) {
      console.error("Failed to delete chat:", error);
    }
  };

  if (chats === undefined) {
    return (
      <View style={styles.container}>
        <ChatHeader
          headerOpacity={new Animated.Value(1)}
          searchOpacity={new Animated.Value(0)}
          searchAnimation={searchAnimation}
          isSearchActive={isSearchActive}
          searchQuery={searchQuery}
          searchInputRef={searchInputRef as React.RefObject<TextInput>}
          setSearchQuery={setSearchQuery}
          toggleSearch={toggleSearch}
        />
        <ChatListSkeleton />
      </View>
    );
  }

  if (!chats?.length) {
    return <MessagesEmptyState />;
  }

  const sortedChats = [...chats].sort(
    (a, b) => b.lastMessageAt - a.lastMessageAt,
  );

  const filteredChats = searchQuery
    ? sortedChats.filter((chat) => {
        const title = chat.isGroup
          ? chat.title || ""
          : chat.otherUser?.name || "";
        return title.toLowerCase().includes(searchQuery.toLowerCase());
      })
    : sortedChats;

  const headerOpacity = searchAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [1, 0],
  });

  const searchOpacity = searchAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  return (
    <GestureHandlerRootView style={styles.container}>
      <ChatHeader
        headerOpacity={headerOpacity}
        searchOpacity={searchOpacity}
        searchAnimation={searchAnimation}
        isSearchActive={isSearchActive}
        searchQuery={searchQuery}
        searchInputRef={searchInputRef as React.RefObject<TextInput>}
        setSearchQuery={setSearchQuery}
        toggleSearch={toggleSearch}
      />

      <View style={styles.listContainer}>
        {filteredChats.map((chat) => {
          if (!chat || !chat._id) return null;
          return (
            <ChatItem
              key={chat._id}
              item={chat}
              onDelete={() => handleDeleteChat(chat._id)}
            />
          );
        })}
      </View>

      <TouchableOpacity
        style={styles.floatingButton}
        onPress={handleOpenCompose}
      >
        <Ionicons name="create" size={24} color="#fff" />
      </TouchableOpacity>

      <ComposeMessageSheet
        isOpen={isComposeOpen}
        onClose={handleCloseCompose}
        onChatCreated={handleChatCreated}
      />
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1C1C1E",
    width: "100%",
  },
  header: {
    flexDirection: "row",
    width: "100%",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 12,
    borderBottomWidth: 0.5,
    borderBottomColor: "#27272A",
    position: "relative",
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: "700",
    color: "#fff",
    letterSpacing: -0.5,
  },
  headerButtons: {
    flexDirection: "row",
    gap: 20,
    position: "absolute",
    right: 16,
  },
  headerButton: {
    padding: 8,
  },
  listContainer: {
    flex: 1,
  },
  chatItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: "#27272A",
  },
  avatar: {
    width: 52,
    height: 52,
    borderRadius: 26,
    backgroundColor: "#27272A",
  },
  groupAvatarContainer: {
    width: 52,
    height: 52,
    position: "relative",
    backgroundColor: "#27272A",
    borderRadius: 26,
    padding: 2,
  },
  groupAvatar: {
    width: 26,
    height: 26,
    borderRadius: 13,
    position: "absolute",
    backgroundColor: "#27272A",
    borderWidth: 1,
    borderColor: "#1C1C1E",
  },
  chatInfo: {
    flex: 1,
    marginLeft: 14,
    justifyContent: "center",
  },
  topLine: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 6,
  },
  bottomLine: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  messageTitle: {
    fontSize: 17,
    fontWeight: "600",
    color: "#fff",
    flex: 1,
    marginRight: 8,
    letterSpacing: -0.3,
  },
  timeText: {
    fontSize: 14,
    color: "#8E8E93",
    fontWeight: "500",
  },
  lastMessage: {
    fontSize: 15,
    color: "#8E8E93",
    flex: 1,
    marginRight: 8,
  },
  unreadBadge: {
    backgroundColor: "#0A84FF",
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 8,
  },
  unreadCount: {
    color: "#fff",
    fontSize: 13,
    fontWeight: "600",
  },
  deleteAction: {
    backgroundColor: "#FF3B30",
    justifyContent: "center",
    alignItems: "center",
    width: 80,
    height: "100%",
  },
  titleContainer: {
    flex: 1,
    paddingRight: 100,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#27272A",
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 38,
    flex: 1,
    marginRight: 100,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    color: "#fff",
    fontSize: 17,
    padding: 0,
    height: "100%",
  },
  floatingButton: {
    position: "absolute",
    right: 20,
    bottom: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: "#0A84FF",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  avatarFallback: {
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
  },
  avatarLetter: {
    color: "#fff",
    fontWeight: "600",
    letterSpacing: -0.5,
  },
});
