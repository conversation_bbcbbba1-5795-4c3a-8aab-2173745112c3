import React, { useEffect, useState, useRef } from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
// import { useMutation, useQuery } from "convex/react";
// import { api } from "@workspace/backend/convex/_generated/api";
import { Id } from "@workspace/backend/convex/_generated/dataModel";
import { Ionicons } from "@expo/vector-icons";
import UserAvatar from "./ui/user-avatar";
import { useUser } from "../hooks/useUser";

interface PresenceTrackerProps {
  streamId: Id<"streams">;
  onViewerListToggle?: () => void;
  showAvatars?: boolean;
  showCountOnly?: boolean;
  overrideCount?: number;
  isLive?: boolean;
}

export default function PresenceTracker({
  streamId,
  onViewerListToggle,
  showAvatars = true,
  showCountOnly = false,
  overrideCount,
  isLive,
}: PresenceTrackerProps) {
  // Get current user information
  const { user } = useUser();

  // Use a longer interval between polls to reduce load
  const pollInterval = 8000; // 8 seconds between refreshes

  // Maintain local state for count to avoid UI jumps
  const [localViewerCount, setLocalViewerCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Get stream viewers
  // TODO: Implement getStreamViewers API function
  const viewers = [] as any[];

  // Track previous counts for smoothing
  const prevCountRef = useRef(0);

  // Smooth viewer count transitions for better UX
  useEffect(() => {
    if (viewers !== undefined) {
      setIsLoading(false);

      // If viewer count hasn't changed, don't update
      // This prevents flicker when backend temporarily reports wrong count
      if (
        prevCountRef.current === viewers.length &&
        localViewerCount === viewers.length
      ) {
        return;
      }

      // Only update the count if it's within a reasonable range of previous count
      // or if this is our first load
      const isDramaticChange =
        Math.abs(viewers.length - prevCountRef.current) > 3 &&
        prevCountRef.current > 0;

      if (!isDramaticChange || isLoading) {
        setLocalViewerCount(viewers.length);
        prevCountRef.current = viewers.length;
      }
    }
  }, [viewers, isLoading]);

  // Auto-refresh viewer count on a timer
  useEffect(() => {
    // Don't auto-poll if no stream ID
    if (!streamId) return;

    // Set up polling interval
    const interval = setInterval(() => {
      // We rely on Convex's built-in refreshing rather than manually triggering
    }, pollInterval);

    return () => clearInterval(interval);
  }, [streamId]);

  // Use provided override count if available
  const displayCount =
    overrideCount !== undefined ? overrideCount : localViewerCount;

  // Just show the viewer count
  if (showCountOnly) {
    return (
      <View style={styles.viewerCountContainer}>
        {isLive && <View style={styles.liveDot}></View>}
        <Ionicons
          name="people-outline"
          size={16}
          color="#fff"
          style={styles.icon}
        />
        <Text style={styles.count}>{isLoading ? "..." : displayCount}</Text>
      </View>
    );
  }

  // Show the full viewer component with optional avatars
  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onViewerListToggle}
      disabled={!onViewerListToggle}
    >
      <Ionicons name="eye-outline" size={16} color="#fff" style={styles.icon} />
      <Text style={styles.count}>{isLoading ? "..." : displayCount}</Text>

      {showAvatars && viewers && viewers.length > 0 && (
        <View style={styles.avatarRow}>
          {viewers.slice(0, 3).map((viewer, index) => (
            <View
              key={`viewer-${index}`}
              style={[styles.avatarContainer, { zIndex: 10 - index }]}
            >
              <UserAvatar image={viewer.image || undefined} size={22} />
            </View>
          ))}

          {viewers.length > 3 && (
            <View style={[styles.avatarContainer, styles.moreAvatars]}>
              <Text style={styles.moreText}>+{viewers.length - 3}</Text>
            </View>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.4)",
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 5,
  },
  icon: {
    marginRight: 4,
  },
  count: {
    color: "#fff",
    fontWeight: "600",
    fontSize: 14,
  },
  avatarRow: {
    flexDirection: "row",
    marginLeft: 6,
  },
  avatarContainer: {
    marginLeft: -8,
    borderWidth: 1,
    borderColor: "#000",
    borderRadius: 11,
  },
  moreAvatars: {
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: "#555",
    justifyContent: "center",
    alignItems: "center",
  },
  moreText: {
    color: "#fff",
    fontSize: 8,
    fontWeight: "bold",
  },
  liveDot: {
    width: 6,
    height: 6,
    borderRadius: 5,
    backgroundColor: "#FF0000",
    marginRight: 4,
  },
  viewerCountContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.4)",
    borderRadius: 16,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderWidth: 1,
    borderColor: "#BBB",
  },
});
