export const CURRENCIES = {
  USD: "usd",
  EUR: "eur",
} as const;

export const INTERVALS = {
  MONTH: "month",
  YEAR: "year",
} as const;

export const PLANS = {
  FREE: "free",
  STANDARD: "standard",
  PRO: "pro",
  ENTERPRISE: "enterprise",
} as const;

export const ROLES = {
  ADMIN: "admin",
  MEMBER: "member",
  GUEST: "guest",
  BLOCKED: "blocked",
} as const;

export const PERMISSIONS = {
  ROOM: {
    CREATE: "room:create",
    DELETE: "room:delete",
    UPDATE: "room:update",
    BLOCK_USER: "room:block_user",
    UNBLOCK_USER: "room:unblock_user",
    MUTE_USER: "room:mute_user",
    UNMUTE_USER: "room:unmute_user",
    MUTE_ALL: "room:mute_all",
    UNMUTE_ALL: "room:unmute_all",
    MUTE_NOTIFICATIONS: "room:mute_notifications",
    UNMUTE_NOTIFICATIONS: "room:unmute_notifications",
    JOIN: "room:join",
  },
  USER: {
    UPDATE: "user:update",
    DELETE: "user:delete",
  },
} as const;

export const PRESENCE_LIST_LIMIT = 20;
export const PRESENCE_MARK_AS_GONE_MS = 8_000;

export const categories = [
  { id: "trading_cards", title: "Trading Cards" },
  { id: "sports_cards", title: "Sports Cards" },
  { id: "sports_memorabilia", title: "Sports Memorabilia" },
  { id: "toys_hobbies", title: "Toys & Hobbies" },
  { id: "coins_notes", title: "Coins & Notes" },
  { id: "storage_garage_sales", title: "Storage & Garage Sales" },
  { id: "handmade_crafts", title: "Handmade & Crafts" },
  { id: "comics", title: "Comics" },
  { id: "electronics", title: "Electronics" },
  { id: "mens_attire", title: "Men's Attire" },
  { id: "kicks_urban_attire", title: "Kicks & Urban Attire" },
  { id: "luxury_bags_accessories", title: "Luxury Bags & Accessories" },
  { id: "womens_attire", title: "Women's Attire" },
  { id: "cosmetic_artistry", title: "Cosmetic Artistry" },
  { id: "jewels_gems", title: "Jewels & Gems" },
  { id: "music", title: "Music" },
  { id: "video_games", title: "Video Games" },
  { id: "heirlooms_antiquities", title: "Heirlooms & Antiquities" },
  { id: "baby_kids", title: "Baby & Kids" },
  { id: "anime_manga", title: "Anime & Manga" },
  { id: "sports_equipment", title: "Sports Equipment" },
  { id: "pokemon", title: "Pokemon" },
  { id: "motion_pictures", title: "Motion Pictures" },
  { id: "knives_hunting", title: "Knives & Hunting" },
  { id: "horror_genre", title: "Horror Genre" },
  { id: "publications", title: "Publications" },
  { id: "pallet_bazaars", title: "Pallet Bazaars" },
  { id: "drinks_treats", title: "Drinks & Treats" },
  { id: "time_pieces", title: "Time Pieces" },
  { id: "funko_pop", title: "Funko Pop" },
  { id: "disney", title: "Disney" },
];

export const subcategories: Record<string, any[]> = {
  trading_cards: [
    { id: "magic", title: "Magic: The Gathering" },
    { id: "yugioh", title: "Yu-Gi-Oh! Cards" },
    { id: "one_piece", title: "One Piece Cards" },
    { id: "disney", title: "Disney Cards" },
    { id: "weiss_schwarz", title: "Weiß Schwarz" },
    { id: "dragon_ball", title: "Dragon Ball Cards" },
    { id: "veefriends", title: "VeeFriends" },
    { id: "naruto", title: "Naruto Cards" },
    { id: "star_wars", title: "Star Wars Cards" },
    { id: "marvel", title: "Marvel Cards" },
    { id: "dc", title: "DC Cards" },
    { id: "digimon", title: "Digimon Cards" },
    { id: "metazoo", title: "MetaZoo" },
    { id: "my_hero_academia", title: "My Hero Academia Cards" },
    { id: "flesh_and_blood", title: "Flesh & Blood" },
    { id: "kryptik", title: "Kryptik" },
    { id: "akora", title: "Akora" },
    { id: "garbage_pail_kids", title: "Garbage Pail Kids" },
    { id: "union_arena", title: "Union Arena" },
    { id: "sorcery", title: "Sorcery: Contested Realm" },
    { id: "other_tcg", title: "Other TCG" },
  ],
  sports_cards: [
    { id: "football", title: "Football Cards" },
    { id: "basketball", title: "Basketball Cards" },
    { id: "baseball", title: "Baseball Cards" },
    { id: "soccer", title: "Soccer Cards" },
    { id: "ufc", title: "UFC Cards" },
    { id: "hockey", title: "Hockey Cards" },
    { id: "wrestling", title: "Wrestling Cards" },
    { id: "motorsport", title: "Motorsport Cards" },
    { id: "other_sports", title: "Other Sports Cards" },
  ],
  sports_memorabilia: [
    { id: "football_mem", title: "Football Memorabilia" },
    { id: "basketball_mem", title: "Basketball Memorabilia" },
    { id: "baseball_mem", title: "Baseball Memorabilia" },
    { id: "soccer_mem", title: "Soccer Memorabilia" },
    { id: "other_sports_mem", title: "Other Sports Memorabilia" },
  ],
  toys_hobbies: [
    { id: "action_figures", title: "Action Figures" },
    { id: "lego", title: "LEGO" },
    { id: "diecast", title: "Diecast" },
    { id: "bearbrick", title: "Bearbrick" },
    { id: "designer_toys", title: "Other Designer Toys" },
    { id: "pop_mart", title: "Pop Mart" },
    { id: "kawaii", title: "Kawaii" },
    { id: "sonny_angels", title: "Sonny Angels & Smiskis" },
    { id: "plush", title: "Plush" },
    { id: "star_wars_toys", title: "Star Wars" },
    { id: "slot_cars", title: "Slot Cars" },
    { id: "dolls", title: "Dolls" },
    { id: "rc_vehicles", title: "Radio Control Vehicles & Toys" },
    { id: "models", title: "Models & Kits" },
    { id: "figpin", title: "FigPin" },
    { id: "fast_food_toys", title: "Fast Food & Cereal Toys" },
    { id: "vintage_toys", title: "Vintage Toys" },
    { id: "other_toys", title: "Other Toys" },
  ],
  coins_notes: [
    { id: "coins_bullion", title: "Coins & Bullion" },
    { id: "bnta_dealers", title: "BNTA Dealers" },
    { id: "paper_money", title: "Paper Money & Currency" },
    { id: "exonumia", title: "Exonumia" },
  ],
  storage_garage_sales: [
    { id: "estate_sales", title: "Estate Sales" },
    { id: "storage_units", title: "Storage Unit Finds" },
    { id: "deal_hunting", title: "Deal Hunting" },
    { id: "garage_sales", title: "Garage Sales" },
    { id: "other_estate", title: "Other Estate Sales & Storage Units" },
  ],
  handmade_crafts: [
    { id: "craft_supplies", title: "Craft Supplies" },
    { id: "handmade", title: "Handmade Items" },
  ],
  comics: [
    { id: "comic_books", title: "Comic Books" },
    { id: "graphic_novels", title: "Graphic Novels" },
    { id: "comic_collectibles", title: "Comic Collectibles" },
  ],
  electronics: [
    { id: "everyday_electronics", title: "Everyday Electronics" },
    { id: "cameras", title: "Cameras & Photography" },
  ],
  mens_attire: [
    { id: "mens_vintage", title: "Men's Vintage Clothing" },
    { id: "mens_modern", title: "Men's Modern" },
    { id: "sports_jerseys", title: "Sports Jerseys" },
    { id: "other_mens", title: "Other Men's Fashion" },
  ],
  kicks_urban_attire: [
    { id: "sneakers", title: "Sneakers" },
    { id: "streetwear", title: "Streetwear" },
  ],
  luxury_bags_accessories: [
    { id: "luxury_bags", title: "Luxury Bags & Accessories" },
    { id: "midrange_bags", title: "Midrange & Fashion Bags" },
    { id: "other_accessories", title: "Other Accessories" },
  ],
  womens_attire: [
    { id: "womens_vintage", title: "Women's Vintage Clothing" },
    { id: "womens_contemporary", title: "Women's Contemporary" },
    { id: "womens_activewear", title: "Women's Activewear" },
    { id: "other_womens", title: "Other Women's Fashion" },
  ],
  cosmetic_artistry: [
    { id: "makeup_skincare", title: "Makeup & Skincare" },
    { id: "nails", title: "Nails" },
    { id: "fragrances", title: "Fragrances & Perfume" },
    { id: "hair", title: "Hair Products" },
    { id: "other_cosmetics", title: "Other Cosmetic Products" },
  ],
  jewels_gems: [
    { id: "fine_jewelry", title: "Fine & Precious Metals" },
    { id: "vintage_jewelry", title: "Vintage & Antique Jewelry" },
    { id: "costume_jewelry", title: "Contemporary Costume" },
    { id: "artisan_jewelry", title: "Handcrafted & Artisan Jewelry" },
    { id: "mens_jewelry", title: "Men's Jewelry" },
  ],
  music: [
    { id: "vinyl", title: "Vinyl Records" },
    { id: "cds_cassettes", title: "CDs & Cassettes" },
    { id: "music_memorabilia", title: "Music Memorabilia" },
    { id: "instruments", title: "Instruments & Accessories" },
    { id: "other_music", title: "Other Music" },
  ],
  video_games: [
    { id: "retro_games", title: "Retro Games" },
    { id: "modern_games", title: "Modern Games" },
    { id: "consoles", title: "Consoles & Accessories" },
    { id: "game_guides", title: "Strategy Guides, Manuals, Replacement Cases" },
  ],
  heirlooms_antiquities: [
    { id: "vintage_decor", title: "Vintage Decor" },
    { id: "antiques", title: "Antiques" },
  ],
  baby_kids: [
    { id: "baby_items", title: "Baby Items" },
    { id: "kids_toys", title: "Kids Toys" },
    { id: "other_baby_kids", title: "Other Baby & Kids" },
  ],
  anime_manga: [
    { id: "anime_figures", title: "Anime Figures" },
    { id: "anime_collectibles", title: "Anime Collectibles" },
    { id: "other_anime_manga", title: "Other Anime & Manga" },
  ],
  sports_equipment: [
    { id: "golf", title: "Golf" },
    { id: "fishing", title: "Fishing" },
    { id: "disc_golf", title: "Disc Golf" },
    { id: "pickleball", title: "Pickleball" },
    { id: "other_sporting", title: "Other Sporting Goods" },
  ],
  pokemon: [
    { id: "pokemon_cards", title: "Pokemon Cards" },
    { id: "pokemon_collectibles", title: "Pokemon Collectibles" },
    { id: "other_pokemon", title: "Other Pokemon" },
  ],
  motion_pictures: [
    { id: "movies", title: "Movies" },
    { id: "tv_shows", title: "TV Shows" },
    { id: "other_motion_pictures", title: "Other Motion Pictures" },
  ],
  knives_hunting: [
    { id: "knives", title: "Knives" },
    { id: "hunting", title: "Hunting" },
  ],
  horror_genre: [
    { id: "horror_movies", title: "Horror Movies" },
    { id: "horror_collectibles", title: "Horror Collectibles" },
  ],
  publications: [
    { id: "magazines", title: "Magazines" },
    { id: "newspapers", title: "Newspapers" },
    { id: "other_publications", title: "Other Publications" },
  ],
  pallet_bazaars: [
    { id: "pallet_bazaars", title: "Pallet Bazaars" },
    { id: "other_pallet_bazaars", title: "Other Pallet Bazaars" },
  ],
  drinks_treats: [
    { id: "drinks", title: "Drinks" },
    { id: "treats", title: "Treats" },
  ],
  time_pieces: [
    { id: "watches", title: "Watches" },
    { id: "clocks", title: "Clocks" },
    { id: "other_time_pieces", title: "Other Time Pieces" },
  ],
  funko_pop: [
    { id: "funko_pop", title: "Funko Pop" },
    { id: "other_funko_pop", title: "Other Funko Pop" },
  ],
  disney: [
    { id: "disney_collectibles", title: "Disney Collectibles" },
    { id: "other_disney", title: "Other Disney" },
  ],
  other: [
    { id: "home_decor", title: "Home Decor" },
    { id: "kitchen_dining", title: "Kitchen & Dining" },
    { id: "books", title: "Books" },
    { id: "candles", title: "Candles" },
    { id: "drinks_snacks", title: "Drinks & Snacks" },
    { id: "motorsports", title: "Motorsports and Cars" },
    { id: "movies", title: "Movies" },
    { id: "pets", title: "Pets" },
    { id: "ephemera", title: "Ephemera" },
    { id: "hydration", title: "Hydration" },
    { id: "watches", title: "Watches" },
    { id: "board_games", title: "Board Games and Puzzles" },
    { id: "tools", title: "Tools and Woodworking" },
    { id: "community", title: "Community" },
    { id: "everything_else", title: "Everything Else" },
  ],
};