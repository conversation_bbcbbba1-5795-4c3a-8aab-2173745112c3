import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getCurrentUser } from "./helpers/utils";
import { Id } from "./_generated/dataModel";

/**
 * @name createNotification
 * @description Create a notification for a user
 */
export const createNotification = mutation({
  args: {
    userId: v.id("users"),
    type: v.string(),
    data: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) {
      throw new Error("Not authenticated");
    }

    const notification = await ctx.db.insert("notifications", {
      userId: args.userId,
      type: args.type,
      actorId: user._id,
      read: false,
      ...(args.data !== undefined ? { data: args.data } : {}),
    });

    return notification;
  }
});

/**
 * @name getNotifications
 * @description Get notifications for the current user
 */
export const getNotifications = query({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) return [];

    const notifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .order("desc")
      .collect();

    const notificationsWithActors = await Promise.all(
      notifications.map(async (notification) => {
        const actor = await ctx.db.get(notification.actorId as Id<"users"> || notification.userId as Id<"users">);
        if (!actor) return null;

        let avatarUrl = null;
        if (actor.image) {
          if (typeof actor.image === 'string' && (actor.image.startsWith('http://') || actor.image.startsWith('https://'))) {
            avatarUrl = actor.image;
          } else {
            try {
              avatarUrl = await ctx.storage.getUrl(actor.image);
            } catch (error) {
              console.error(`Error getting avatar URL for user ${actor._id}:`, error);
              avatarUrl = null;
            }
          }
        }

        return {
          ...notification,
          actor: {
            ...actor,
            avatarUrl
          },
        };
      })
    );

    const validNotifications = notificationsWithActors.filter(
      (n): n is NonNullable<typeof n> => n !== null,
    );

    return validNotifications.sort((a, b) => b._creationTime - a._creationTime);
  },
});

/**
 * @name markAsRead
 * @description Mark a notification as read
 */
export const markAsRead = mutation({
  args: {
    notificationId: v.id("notifications"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const notification = await ctx.db.get(args.notificationId);
    if (!notification) throw new Error("Notification not found");

    if (notification.userId !== user._id) {
      throw new Error("Not authorized to mark this notification as read");
    }

    await ctx.db.patch(args.notificationId, { read: true });
    return { success: true };
  },
});

/**
 * @name markAllAsRead
 * @description Mark all notifications as read for the current user
 */
export const markAllAsRead = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const unreadNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("read"), false))
      .collect();

    await Promise.all(
      unreadNotifications.map((notification) =>
        ctx.db.patch(notification._id, { read: true }),
      ),
    );

    return { count: unreadNotifications.length };
  },
});

/**
 * @name deleteNotification
 * @description Delete a notification
 */
export const deleteNotification = mutation({
  args: {
    notificationId: v.id("notifications"),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    if (!user) throw new Error("Not authenticated");

    const notification = await ctx.db.get(args.notificationId);
    if (!notification) throw new Error("Notification not found");

    if (notification.userId !== user._id) {
      throw new Error("Not authorized to delete this notification");
    }

    await ctx.db.delete(args.notificationId);
    return { success: true };
  },
}); 