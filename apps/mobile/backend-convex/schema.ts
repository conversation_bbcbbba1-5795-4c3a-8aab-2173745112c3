import { defineSchema, defineTable } from "convex/server";
import { authTables } from "@convex-dev/auth/server";
import { v } from "convex/values";
import {
  CURRENCY_VALIDATOR,
  LOGIN_TYPE,
  PRODUCT_CONDITION,
  PRODUCT_VISIBILITY,
  USER_STATUS,
} from "./lib/validators.js";
import { TASK_PRIORITY } from "./lib/types.js";
import { TASK_STATUS } from "./lib/types.js";

const schema = defineSchema({
  ...authTables,

  /**
   * @name interestedSellers
   * @description Stores information about users interested in becoming sellers
   * @index by_user
   * @index by_email
   */
  interestedSellers: defineTable({
    userId: v.optional(v.id("users")),
    email: v.string(),
    category: v.optional(v.string()),
    subcategories: v.optional(v.array(v.string())),
    currentlySellingOther: v.optional(v.boolean()),
    hasSellingExperience: v.boolean(),
    platform: v.optional(v.string()),
    platformLink: v.optional(v.string()),
    monthlyRevenue: v.optional(v.string()),
    primaryGoal: v.optional(v.string()),
    socialMedia: v.optional(v.array(
      v.object({
        platform: v.string(),
        username: v.optional(v.string()),
      })
    )),
    additionalInfo: v.optional(v.string()),
    submittedAt: v.number(),
    status: v.optional(v.union(
      v.literal("new"),
      v.literal("contacted"),
      v.literal("approved"),
      v.literal("rejected")
    )),
    notes: v.optional(v.string()),
  })
    .index("by_user", ["userId"])
    .index("by_email", ["email"])
    .index("by_status", ["status"])
    .index("by_experience", ["hasSellingExperience"]),

  /**
   * @name users
   * @index by_email
   * @searchIndex search_name
   * @searchIndex search_email
   * @searchIndex search_phone
   */
  users: defineTable({
    name: v.optional(v.string()),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    email: v.string(),
    phone: v.optional(v.string()),
    image: v.optional(v.string()),
    coverImage: v.optional(v.string()),
    emailVerificationTime: v.optional(v.number()),
    phoneVerificationTime: v.optional(v.number()),
    isAnonymous: v.optional(v.boolean()),
    lastLoginType: v.optional(LOGIN_TYPE),
    finishedSignUp: v.optional(v.boolean()),
    isSellerInterested: v.optional(v.boolean()),
    color: v.optional(v.string()),
    theme: v.optional(v.string()),
    lastSeen: v.optional(v.number()),
    status: v.optional(USER_STATUS),
    safetyGuidelinesAgreement: v.optional(
      v.object({
        agreed: v.boolean(),
        agreedAt: v.number(),
      }),
    ),
    username: v.optional(v.string()),
    role: v.union(v.literal("user"), v.literal("seller"), v.literal("admin")),
    bio: v.optional(v.string()),
    sellerProfile: v.optional(
      v.object({
        categories: v.optional(v.array(v.string())),
        verified: v.optional(v.boolean()),
        joinedAt: v.optional(v.number()),
        percentage: v.optional(v.number()),
      }),
    ),
    preferences: v.optional(
      v.object({
        notifications: v.optional(v.boolean()),
        emailUpdates: v.optional(v.boolean()),
        darkMode: v.optional(v.boolean()),
        categories: v.optional(v.array(v.string())),
        subcategories: v.optional(v.array(v.string())),
        shippingAddresses: v.optional(
          v.array(
            v.object({
              street: v.string(),
              address2: v.optional(v.string()),
              city: v.string(),
              state: v.string(),
              country: v.string(),
              zipCode: v.string(),
              isDefault: v.optional(v.boolean()),
              fullName: v.optional(v.string()),
              isReturn: v.optional(v.boolean()),
            }),
          ),
        ),
      }),
    ),
    stripeAccountId: v.optional(v.string()),
    stripeCustomerId: v.optional(v.string()),
    paymentMethods: v.optional(v.array(v.object({
      id: v.string(),
      last4: v.string(),
      brand: v.union(v.literal("amex"), v.literal("diners"), v.literal("discover"), v.literal("jcb"), v.literal("mastercard"), v.literal("unionpay"), v.literal("visa"), v.literal("unknown")),
      expMonth: v.number(),
      expYear: v.number(),
      isDefault: v.boolean(),
    }))),
  })
    .index("by_email", ["email"])
    .index("by_role", ["role"])
    .searchIndex("search_name", {
      searchField: "name",
      filterFields: ["name"],
    })
    .searchIndex("search_email", {
      searchField: "email",
      filterFields: ["email"],
    })
    .searchIndex("search_phone", {
      searchField: "phone",
      filterFields: ["phone"],
    })
    .searchIndex("search_users", {
      searchField: "username",
      filterFields: ["email", "username"],
    }),

  /**
   * @name verificationCodes
   * @description Stores temporary verification codes for phone number verification
   * @index by_user_id
   * @index by_phone
   */
  verificationCodes: defineTable({
    userId: v.id("users"),
    code: v.string(),
    phone: v.string(),
    expiresAt: v.number(),
    verified: v.optional(v.boolean()),
    attempts: v.optional(v.number()),
  })
    .index("by_user_id", ["userId"])
    .index("by_phone", ["phone"])
    .index("by_expiry", ["expiresAt"]),

  /**
   * @name follows
   * @description Follow relationships between users
   */
  follows: defineTable({
    followerId: v.id("users"),
    followingId: v.id("users"),
    userId: v.optional(v.id("users")),
  })
    .index("by_follower", ["followerId"])
    .index("by_following", ["followingId"])
    .index("unique_follow", ["followerId", "followingId"])
    .index("by_user", ["userId"]),

  /**
   * @name blocks
   * @description Block relationships between users
   */
  blocks: defineTable({
    blockerId: v.id("users"),
    blockedId: v.id("users"),
  })
    .index("by_blocker", ["blockerId"])
    .index("by_blocked", ["blockedId"])
    .index("by_both", ["blockerId", "blockedId"]),

  streams: defineTable({
    hostId: v.id("users"),
    title: v.string(),
    description: v.optional(v.string()),
    roomName: v.string(), 
    category: v.optional(v.string()), 
    subcategory: v.optional(v.string()),
    status: v.optional(v.string()), 
    isActive: v.boolean(), 
    productId: v.optional(v.id("products")), 
    scheduledTime: v.optional(v.number()), 
    moderatorIds: v.optional(v.array(v.id("users"))),
    blockedUserIds: v.optional(v.array(v.id("users"))),
    thumbnail: v.optional(v.id("_storage")),
    format: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    explicitContent: v.optional(v.boolean()),
    muteWords: v.optional(v.array(v.string())),
    visibility: v.optional(v.string()),
    eventType: v.optional(v.string()),
    ingestUrl: v.optional(v.string()),
    streamKey: v.optional(v.string()),
  })
    .index("by_hostId", ["hostId"])
    .index("by_status", ["status"]) 
    .index("by_isActive", ["isActive"]) 
    .index("by_roomName", ["roomName"])
    .index("by_category_and_status", ["category", "status"]),

  streamMessages: defineTable({
    streamId: v.id("streams"),
    userId: v.id("users"),
    userName: v.string(),
    text: v.string(),
    type: v.optional(v.string()),
  }).index("by_streamId", ["streamId"]),

  products: defineTable({
    sellerId: v.id("users"),
    name: v.string(),
    description: v.optional(v.string()),
    price: v.optional(v.number()),
    currency: CURRENCY_VALIDATOR,
    inventory: v.optional(v.number()),
    category: v.optional(v.string()),
    condition: PRODUCT_CONDITION,
    images: v.optional(v.array(v.id("_storage"))),
    status: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    streamId: v.optional(v.array(v.id("streams"))),
    subcategory: v.optional(v.string()),
    flashSale: v.optional(v.boolean()),
    acceptOffers: v.optional(v.boolean()),
    reserveForLive: v.optional(v.boolean()),
    shippingProfile: v.optional(v.string()),
    hasHazardousMaterials: v.optional(v.boolean()),
    variants: v.optional(v.array(v.object({
      color: v.optional(v.string()),
      size: v.optional(v.string()),
      quantity: v.optional(v.number()),
    }))),
    quantity: v.optional(v.number()),
    isAuction: v.optional(v.boolean()),
    startingBid: v.optional(v.number()),
    suddenDeath: v.optional(v.boolean()),
    visibility: PRODUCT_VISIBILITY,
  })
    .index("by_sellerId_and_status", ["sellerId", "status"])
    .index("by_status", ["status"]),

  phoneVerifications: defineTable({
    userId: v.id("users"),
    phone: v.string(),
  }).index("by_user", ["userId"]),

  /**
   * @name chats
   * @description Chat rooms table
   * @index by_participant_and_time
   * @index by_createdBy_lastMessageAt
   * @index by_participant
   */
  chats: defineTable({
    title: v.optional(v.string()),
    createdBy: v.id("users"),
    participants: v.array(v.id("users")),
    lastMessageAt: v.number(),
    isGroup: v.boolean(),
  })
    .index("by_participant_and_time", ["participants", "lastMessageAt"])
    .index("by_createdBy_lastMessageAt", ["createdBy", "lastMessageAt"])
    .index("by_participant", ["participants"]),

  /**
   * @name messages
   * @description Messages table
   * @index by_chat
   */
  messages: defineTable({
    chatId: v.id("chats"),
    senderId: v.id("users"),
    text: v.string(),
    image: v.optional(v.id("_storage")),
    readAt: v.optional(v.number()),
  }).index("by_chat", ["chatId"]),

  /**
   * @name notifications
   * @description Stores notifications for users (e.g., mentions in chat)
   * @index by_user
   */
  notifications: defineTable({
    userId: v.optional(v.id("users")),
    actorId: v.optional(v.id("users")),
    type: v.optional(v.string()),
    message: v.optional(v.string()),
    streamId: v.optional(v.id("streams")),
    fromUserId: v.optional(v.id("users")),
    read: v.optional(v.boolean()),
    data: v.optional(v.any()),
  })
    .index("by_user", ["userId"]),

  /**
   * @name streamBans
   * @description Banned users per stream (for moderation)
   * @index by_stream
   * @index by_user
   */
  streamBans: defineTable({
    streamId: v.id("streams"),
    userId: v.id("users"),
    bannedAt: v.number(),
    reason: v.optional(v.string()),
    bannedBy: v.optional(v.id("users")),
  })
    .index("by_stream", ["streamId"])
    .index("by_user", ["userId"]),

  /**
   * @name streamFlaggedMessages
   * @description Flagged messages for moderation review
   * @index by_stream
   * @index by_message
   */
  streamFlaggedMessages: defineTable({
    streamId: v.id("streams"),
    messageId: v.id("streamMessages"),
    flaggedBy: v.id("users"),
    flaggedAt: v.number(),
    reason: v.optional(v.string()),
    reviewed: v.optional(v.boolean()),
    reviewedBy: v.optional(v.id("users")),
    reviewedAt: v.optional(v.number()),
    actionTaken: v.optional(v.string()),
  })
    .index("by_stream", ["streamId"])
    .index("by_message", ["messageId"]),

  /**
   * @name reports
   * @description Reports table
   * @index by_status
   * @index by_reported_user
   * @index by_reporter
   */
  reports: defineTable({
    contentType: v.string(),
    contentId: v.string(),
    reportedUserId: v.id("users"),
    reportedByUserId: v.id("users"),
    reason: v.string(),
    additionalDetails: v.optional(v.string()),
    status: v.string(),
    updatedAt: v.number(),
  })
    .index("by_status", ["status"])
    .index("by_reported_user", ["reportedUserId"])
    .index("by_reporter", ["reportedByUserId"]),

  /**
   * @name bookmarks
   * @description Tracks which streams users have bookmarked
   * @index by_user
   * @index by_stream
   */
  bookmarks: defineTable({
    userId: v.id("users"),
    streamId: v.id("streams"),
  })
    .index("by_user", ["userId"])
    .index("by_stream", ["streamId"])
    .index("by_user_stream", ["userId", "streamId"]),

  /**
   * @name userReviews
   * @description Reviews left for users (typically sellers)
   * @index by_reviewed_user
   * @index by_reviewer
   */
  userReviews: defineTable({
    reviewedUserId: v.id("users"), 
    reviewerUserId: v.id("users"), 
    overallRating: v.number(), 
    shippingRating: v.optional(v.number()), 
    packagingRating: v.optional(v.number()), 
    accuracyRating: v.optional(v.number()), 
    reviewText: v.string(),
    replyText: v.optional(v.string()), 
    replyAt: v.optional(v.number()),  
    isHidden: v.optional(v.boolean()),
  })
    .index("by_reviewed_user", ["reviewedUserId"])
    .index("by_reviewer", ["reviewerUserId"]),

  /**
   * @name reviewReports
   * @description Reports on user reviews
   * @index by_review
   * @index by_reporter
   */
  reviewReports: defineTable({
    reviewId: v.id("userReviews"),
    reporterUserId: v.id("users"),
    reason: v.string(), 
    additionalInfo: v.optional(v.string()),
    status: v.optional(v.string()), 
  })
    .index("by_review", ["reviewId"])
    .index("by_reporter", ["reporterUserId"]),

  /**
   * @name reviewAppeals
   * @description Seller appeals on reviews
   * @index by_review
   * @index by_seller
   */
  reviewAppeals: defineTable({
    reviewId: v.id("userReviews"),
    sellerUserId: v.id("users"),
    type: v.string(), 
    message: v.string(),
    status: v.optional(v.string()), 
  })
    .index("by_review", ["reviewId"])
    .index("by_seller", ["sellerUserId"]),

  /**
   * @name tasks
   * @description Tasks table
   * @index by_assignee
   * @index by_status
   * @index by_due_date
   * @searchIndex search_title
   * @searchIndex search_description
   */
  tasks: defineTable({
    title: v.string(),
    description: v.optional(v.string()),
    createdBy: v.id("users"),
    dueDate: v.optional(v.number()),
    status: TASK_STATUS,
    lastUpdated: v.optional(v.number()),
    assignee: v.optional(v.union(v.id("users"), v.null())),
    related: v.optional(v.union(v.array(v.id("contacts")), v.null())),
    priority: TASK_PRIORITY,
    position: v.optional(v.number()),
    statusHistory: v.optional(
      v.array(
        v.object({
          status: TASK_STATUS,
          timestamp: v.number(),
          userId: v.id("users"),
        }),
      ),
    ),
  })
    .index("by_assignee", ["assignee"])
    .index("by_status", ["status"])
    .index("by_due_date", ["dueDate"])
    .index("by_position", ["position"])
    .index("by_status_position", ["status", "position"])
    .searchIndex("search_title", {
      searchField: "title",
      filterFields: ["title"],
    })
    .searchIndex("search_description", {
      searchField: "description",
      filterFields: ["description"],
    }),

  /**
   * @name favorites
   * @description Favorites table
   * @index by_object
   * @index by_user
   */
  favorites: defineTable({
    objectId: v.union(v.id("tasks"), v.id("users"), v.id("feedback")),
    userId: v.id("users"),
    folderId: v.optional(v.id("favoriteFolders")),
    position: v.optional(v.number()),
  })
    .index("by_object", ["objectId"])
    .index("by_user", ["userId"]),

  /**
   * @name favoriteFolders
   * @description Favorite folders table
   */
  favoriteFolders: defineTable({
    name: v.string(),
    userId: v.id("users"),
    isOpen: v.optional(v.boolean()),
    position: v.optional(v.number()),
  }).index("by_user", ["userId"]),

  /**
   * @name activities
   * @description Activities table
   * @index by_contact
   * @index by_task
   */
  activities: defineTable({
    type: v.string(),
    description: v.string(),
    createdBy: v.union(v.id("users"), v.literal("system")),
    metadata: v.object({
      expiresAt: v.optional(v.number()),
      link: v.optional(v.string()),
      createdBy: v.optional(v.union(v.id("users"), v.literal("system"))),
      actionRequired: v.optional(v.boolean()),
      status: v.optional(v.union(v.literal("accepted"), v.literal("rejected"))),
      objectId: v.optional(v.union(v.id("contacts"), v.id("tasks"), v.id("users"))),
    }),
  }).index("by_object", ["metadata.objectId"]),

  /**
   * @name columnPreferences
   * @description Column preferences table
   * @index by_column
   */
  columnPreferences: defineTable({
    column: v.string(),
    trackTimeInStatus: v.boolean(),
    showConfetti: v.boolean(),
    hidden: v.boolean(),
    targetTimeInStatus: v.optional(v.number()),
  }).index("by_column", ["column"]),

  /**
   * @name contacts
   * @description Contacts table
   * @index by_email
   * @searchIndex search_name
   * @searchIndex search_email
   */
  contacts: defineTable({
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    email: v.optional(v.string()),
    phone: v.optional(v.string()),
    city: v.optional(v.string()),
    createdBy: v.id("users"),
    title: v.optional(v.string()),
    lastUpdated: v.optional(v.number()),
    tasks: v.optional(v.array(v.id("tasks"))),
    related: v.optional(v.union(v.array(v.id("contacts")), v.null())),
  })
    .index("by_email", ["email"])
    .searchIndex("search_name", {
      searchField: "firstName",
      filterFields: ["firstName", "lastName"],
    })
    .searchIndex("search_email", {
      searchField: "email",
      filterFields: ["email"],
    }),

  /**
   * @name adminNotifications
   * @description Admin notifications table
   * @index by_user
   */
  adminNotifications: defineTable({
    message: v.string(),
    type: v.string(),
    userId: v.id("users"),
    read: v.boolean(),
    handled: v.boolean(),
    metadata: v.optional(
      v.object({
        link: v.optional(v.string()),
        createdBy: v.optional(v.union(v.id("users"), v.literal("system"))),
        actionRequired: v.optional(v.boolean()),
        expiresAt: v.optional(v.number()),
        status: v.optional(
          v.union(v.literal("accepted"), v.literal("rejected")),
        ),
      }),
    ),
    updatedAt: v.optional(v.number()),
    archived: v.boolean(),
  }).index("by_user", ["userId"]),

  /**
   * @name savedSearches
   * @description Saved search queries for users
   * @index by_user
   */
  savedSearches: defineTable({
    userId: v.id("users"),
    searchQuery: v.string(),
    type: v.union(
      v.literal("general"),
      v.literal("user"),
      v.literal("category"),
      v.literal("product")
    ),
    targetId: v.optional(v.string()),
    savedAt: v.number(),
    isPinned: v.optional(v.boolean()),
  }).index("by_user", ["userId"]),

  /**
   * @name feedback
   * @description User submitted feedback (bugs and feature requests)
   * @index by_user
   * @index by_type
   * @index by_status
   */
  feedback: defineTable({
    userId: v.id("users"),
    type: v.union(v.literal("bug"), v.literal("feature_request")),
    message: v.string(),
    url: v.optional(v.string()),
    metadata: v.optional(
      v.object({
        browser: v.optional(v.string()),
        device: v.optional(v.string()),
        os: v.optional(v.string()),
      })
    ),
    status: v.union(
      v.literal("new"),
      v.literal("in_progress"),
      v.literal("done"),
      v.literal("rejected")
    ),
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
    updatedBy: v.optional(v.id("users")),
    notes: v.optional(v.string()),
    position: v.optional(v.number()),
    statusHistory: v.optional(
      v.array(
        v.object({
          status: TASK_STATUS,
          timestamp: v.number(),
          userId: v.id("users"),
        }),
      ),
    ),
  })
    .index("by_user", ["userId"])
    .index("by_type", ["type"])
    .index("by_status", ["status"]),

  /**
   * @name changelog
   * @description System changelog entries to track application updates
   * @index by_version
   */
  changelog: defineTable({
    version: v.string(),
    date: v.string(),
    isPublished: v.boolean(),
    changes: v.array(
      v.object({
        type: v.union(
          v.literal("feature"),
          v.literal("improvement"),
          v.literal("bugfix")
        ),
        description: v.string(),
      })
    ),
    summary: v.optional(v.string()),
  })
    .index("by_version", ["version"])
    .index("by_date", ["date"])
    .index("by_published", ["isPublished"]),

  /**
   * @name obsSettings
   * @description OBS/streaming settings for sellers
   * @index by_user_stream
   */
  obsSettings: defineTable({
    userId: v.id("users"),
    streamId: v.id("streams"),
    obsStreamKey: v.optional(v.string()),
    obsServerUrl: v.optional(v.string()),
    profileName: v.optional(v.string()),
    ingestRegion: v.optional(v.string()),
    updatedAt: v.number(),
  }).index("by_user_stream", ["userId", "streamId"]),

  orders: defineTable({
    buyerId: v.id("users"),
    sellerId: v.id("users"),
    productId: v.id("products"),
    streamId: v.optional(v.id("streams")),
    quantity: v.number(),
    totalAmount: v.number(),
    currency: v.string(),
    status: v.string(),
    shippingAddress: v.object({
      street: v.string(),
      city: v.string(),
      state: v.string(),
      country: v.string(),
      zipCode: v.string(),
    }),
    paymentIntentId: v.string(),
    updatedAt: v.number(),
  }),
});

export default schema;
