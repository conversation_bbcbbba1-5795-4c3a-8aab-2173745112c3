"use node";

import { action } from "../_generated/server";
import { WebhookReceiver } from "livekit-server-sdk";
import { v } from "convex/values";
import { api } from "../_generated/api";
export const handleLivekitWebhook = action({
  args: {
    body: v.string(),
    authorization: v.string(),
  },
  handler: async (ctx, { body, authorization }) => {
    console.log("[LiveKit Webhook] Received webhook event");
    const receiver = new WebhookReceiver(
      process.env.LIVEKIT_API_KEY!,
      process.env.LIVEKIT_API_SECRET!
    );
    let event;
    try {
      event = await receiver.receive(body, authorization);
      console.log("[LiveKit Webhook] Event received:", event.event, "Room:", event.room?.name);
    } catch (err) {
      console.error("[LiveKit Webhook] Invalid webhook signature:", err);
      return { error: "Invalid webhook signature" };
    }
    if (event.event === "ingress_started" || event.event === "ingress_ended") {
      const roomName = event.room?.name;
      console.log("[LiveKit Webhook] Processing ingress event:", event.event, "RoomName:", roomName);
      if (!roomName) {
        console.error("[LiveKit Webhook] Missing roomName in event");
        return { error: "Missing roomName in event" };
      }

      const stream = await ctx.runQuery(api.streams.getStreamByRoomName, { roomName });
      
      if (!stream) {
        console.error("[LiveKit Webhook] Stream not found for roomName:", roomName);
        return { error: "Stream not found for roomName" };
      }

      console.log("[LiveKit Webhook] Found stream:", stream._id, "Setting isLive:", event.event === "ingress_started");
      await ctx.runMutation(api.streams.patchStream, {
        streamId: stream._id,
        data: {
          isLive: event.event === "ingress_started",
          status: event.event === "ingress_started" ? "live" : "ended",
          updatedAt: Date.now(),
        },
      });
      console.log("[LiveKit Webhook] Stream status updated successfully");
    }
    return { success: true };
  },
});